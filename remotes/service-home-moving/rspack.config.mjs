import { getSharedDependencies } from '@btaskee/sdk';
import * as Repack from '@callstack/repack';
import { ReanimatedPlugin } from '@callstack/repack-plugin-reanimated';
import rspack from '@rspack/core';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Rspack configuration enhanced with Re.Pack defaults for React Native.
 *
 * Learn about Rspack configuration: https://rspack.dev/config/
 * Learn about Re.Pack configuration: https://re-pack.dev/docs/guides/configuration
 */

export default (env) => {
  const { mode, platform = process.env.PLATFORM } = env;
  const isDev = mode === 'development';

  if (!platform) {
    throw new Error(
      'Platform is required. Please set PLATFORM environment variable or pass platform in env.',
    );
  }

  return {
    mode,
    context: __dirname,
    entry: './index.js',
    experiments: {
      incremental: mode === 'development',
    },
    resolve: {
      ...Repack.getResolveOptions(),
      alias: {
        '@components': path.resolve(__dirname, 'src/components'),
        '@types': path.resolve(__dirname, 'src/types'),
        '@images': path.resolve(__dirname, 'src/assets/images'),
        '@store': path.resolve(__dirname, 'src/storage'),
        '@screens': path.resolve(__dirname, 'src/screens'),
        '@hooks': path.resolve(__dirname, 'src/hooks'),
        '@navigation': path.resolve(__dirname, 'src/navigation'),
        '@config': path.resolve(__dirname, 'src/config'),
        '@constant': path.resolve(__dirname, 'src/constant'),
        '@i18n': path.resolve(__dirname, 'src/i18n'),
        '@e2e': path.resolve(__dirname, 'e2e'),
      },
    },
    output: {
      path: path.resolve(__dirname, 'dist'),
      uniqueName: 'sas-home-moving',
      publicPath: 'auto',
    },
    module: {
      rules: [
        ...Repack.getJsTransformRules(),
        ...Repack.getAssetTransformRules({ inline: true }),
      ],
    },
    plugins: [
      new Repack.RepackPlugin({
        context: __dirname,
        platform: platform,
      }),
      new ReanimatedPlugin(),
      new Repack.plugins.ModuleFederationPluginV2({
        name: 'homeMoving',
        filename: 'home-moving.container.js.bundle',
        dts: false,
        exposes: {
          './MainNavigator': './src/navigation/MainNavigator.tsx',
        },
        shared: getSharedDependencies({ eager: isDev }),
      }),
      // silence missing @react-native-masked-view optionally required by @react-navigation/elements
      new rspack.IgnorePlugin({
        resourceRegExp: /^@react-native-masked-view/,
      }),
    ],
  };
};
