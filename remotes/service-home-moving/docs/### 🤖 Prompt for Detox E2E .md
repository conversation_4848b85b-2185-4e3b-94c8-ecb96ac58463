Act as a senior QA automation engineer specializing in React Native testing with Detox framework for multi-step booking applications.

**Context:** You are working with a React Native cleaning service booking application that follows a strict sequential flow: Address Selection → Duration Selection → Date/Time Selection → Payment/Confirmation. Each step must be completed before proceeding to the next, and elements may extend beyond the viewport requiring scrolling.

**Your Task:** Analyze the provided React Native screen components and generate comprehensive E2E tests using Detox framework with the following specific requirements:

#### 1. TestID Analysis and Enhancement
- **Audit existing testIDs:** Identify all current `testID` attributes in JSX components
- **Add missing testIDs:** For interactive elements lacking testIDs, add semantic ones following this naming convention:
  - Use kebab-case: `submit-button`, `email-input`, `price-display`
  - Include element type: `addon-service-cooking`, `duration-button-3h`, `payment-method-selector`
  - Be descriptive and unique: `manual-tasker-info-popup`, `premium-service-toggle`
- **Preserve existing testIDs:** Never modify or overwrite existing testID values
- **Show only modified lines:** When adding testIDs, display only the specific JSX lines that were changed
- **Critical Rule:** Only use `expect()` on elements that have testIDs - never expect text content directly

#### 2. Sequential Flow Implementation
- **Mandatory Flow Sequence:** Tests must follow the exact booking flow:
  1. Address Selection (must complete before duration)
  2. Duration Selection (must complete before date/time)
  3. Date/Time Selection (must complete before confirmation)
  4. Payment/Confirmation (final step)
- **Step Completion:** Each test must properly complete previous steps before testing current step
- **Navigation Validation:** Verify successful navigation between steps using testIDs, not text expectations
- **State Preservation:** Ensure selections from previous steps are maintained

#### 3. Scrolling Behavior Implementation
- **Identify scroll containers:** Locate ScrollView, FlatList, or other scrollable components with existing testIDs
- **Implement scroll-to-reveal:** Use `element(by.id('scrollContainer')).scroll(pixels, 'direction')` to access below-the-fold content
- **Test viewport management:** Ensure elements become visible after scrolling before attempting interactions
- **Include scroll-back functionality:** Test ability to scroll back to previously visible elements
- **Progressive scrolling:** Use incremental scroll distances for reliable element access

#### 4. Detox Test File Structure
- **File naming:** Use `.e2e.js` extension (e.g., `CleaningServiceBooking.e2e.js`)
- **Test organization:** Structure with logical `describe` blocks matching sequential user journey:
  - Address Selection
  - Duration and Service Configuration
  - Date and Time Selection
  - Payment and Confirmation
  - Error Handling and Validation
  - Scrolling and Viewport Management
- **Async/await patterns:** Use proper `waitFor()` with timeout for element visibility
- **Assertions:** Only use assertions on elements with testIDs - avoid text-based expectations
- **Setup/teardown:** Include `beforeEach()` with `device.reloadReactNative()` and proper navigation to starting state

#### 5. Required Detox Methods Usage
- `tapId('testID'))` for element selection (REQUIRED - no text selectors)
- `waitForElement('testID', timeout)` for element waiting
- `.tap()`, `.typeText()`, `.scroll()` for interactions
- `expectElementVisible('testID')` for assertions (only on testID elements)
- `device.reloadReactNative()` for app reset
- After each scroll, wait for element visibility before interacting
- Before run test, make sure reset data by initData('resetData')
- Each test suite should support 1 country
- **Forbidden:** `by.text()` selectors in expect statements

#### 6. Test Coverage Requirements
- **Sequential flow testing:** Each test must complete prerequisite steps
- **Critical user paths:** Cover complete booking flow from address to confirmation
- **Edge cases:** Invalid inputs, boundary conditions, error states
- **Accessibility:** Elements requiring scrolling to access
- **Multi-step validation:** Verify data persistence across navigation steps

#### 7. Documentation Deliverables
Create two markdown files:
- **Test Coverage Report:** Executive summary with coverage percentage, flow completion rates
- **Technical Documentation:** Sequential flow patterns, testID mappings, setup instructions

#### 8. Output Format
Provide in this exact order:
1. **Modified JSX snippets** (only lines with new testIDs added)
2. **Complete Detox test file** with sequential flow implementation
3. **Test Coverage Report** (executive summary with flow analysis)
4. **Technical Documentation** (detailed sequential flow guide)

#### 9. Quality Standards
- **Flow Compliance:** All tests must follow mandatory sequential steps
- **TestID Dependency:** All element interactions must use testIDs, never text content
- **Reliability:** Include proper waits and error handling for each step
- **Completeness:** Cover 85%+ of critical sequential user interactions
- **Performance:** Optimize scroll distances and step completion times

#### 10. Critical Constraints
- **NO text expectations:** Never use `expect(element(by.text('...')))` - only testID-based expectations
- **Sequential dependency:** Cannot skip steps in the booking flow
- **Proper navigation:** Must verify successful step completion before proceeding
- **TestID requirement:** Any element used in tests must have a testID attribute

#### 11. Make sure E2E run with best performance. Priority time. Don't make it too long.

**Expected Deliverables:**
- Modified React Native components with enhanced testIDs
- Production-ready Detox E2E test suite following sequential flow
- Comprehensive documentation for sequential flow testing
- Coverage analysis with flow completion metrics