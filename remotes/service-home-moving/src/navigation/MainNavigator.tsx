import '../i18n';

import React, { useCallback, useEffect, useMemo } from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  getDefaultPaymentMethod,
  IconAssets,
  IconImage,
  IService,
  NavBar,
  SERVICES,
  Spacing,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackHeaderProps,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';
import { usePostTaskStore } from '@store';

import { useI18n } from '@hooks';
import {
  ChooseService,
  ConfirmAndPayment,
  IntroService,
  PostTaskSuccess,
  StandardPackagingProcessScreen,
  WorkingDescriptionProgress,
} from '@screens';

import { RouteName } from './RouteName';
import { RootStackParamList } from './types';

const Stack = createNativeStackNavigator<RootStackParamList>();

// Extract component outside of render to prevent reconciliation issues
interface AddressTitleProps {
  shortAddress?: string;
  address?: string;
}

const AddressTitle: React.FC<AddressTitleProps> = React.memo(
  ({ shortAddress, address }) => {
    if (!shortAddress && !address) {
      return null;
    }

    return (
      <BlockView
        flex
        row
        horizontal
      >
        <IconImage
          source={IconAssets.icLocation}
          size={24}
          color={ColorsV2.red500}
        />
        <BlockView margin={{ left: Spacing.SPACE_08 }}>
          <CText color={ColorsV2.neutral400}>{shortAddress}</CText>
          <CText
            fontFamily="bold"
            numberOfLines={1}
            margin={{ right: Spacing.SPACE_16 }}
            color={ColorsV2.neutral800}
          >
            {address}
          </CText>
        </BlockView>
      </BlockView>
    );
  },
);

AddressTitle.displayName = 'AddressTitle';

const MainNavigator = () => {
  const { t } = useI18n();
  const { setService, setPaymentMethod, isFirstOpen } = usePostTaskStore();
  const settings = useSettingsStore().settings;

  // Memoize the home moving service to avoid unnecessary re-computations
  const homeMovingService = useMemo(() => {
    return settings?.services?.find(
      (service: IService) => service?.name === SERVICES.HOME_MOVING,
    );
  }, [settings?.services]);

  // Use useCallback to prevent unnecessary re-renders
  const initData = useCallback(async () => {
    if (homeMovingService) {
      setService(homeMovingService);
    }
    setPaymentMethod(
      getDefaultPaymentMethod({ serviceName: SERVICES.HOME_MOVING }),
    );
  }, [homeMovingService, setService, setPaymentMethod]);

  useEffect(() => {
    initData();
  }, [initData]);

  // Screen options following the established pattern across all microservices
  const screenOptions = useCallback(
    ({ navigation }: any): NativeStackNavigationOptions => ({
      headerShown: true,
      animation: 'slide_from_right',
      animationDuration: 200,
      contentStyle: { backgroundColor: ColorsV2.neutralWhite },
      // eslint-disable-next-line react/no-unstable-nested-components
      header: (props: NativeStackHeaderProps) => {
        // Get the title from options
        const getTitle = () => {
          if (
            props?.options?.headerTitle &&
            typeof props.options.headerTitle === 'function'
          ) {
            // @ts-ignore - React Navigation headerTitle function compatibility
            return props.options.headerTitle();
          }
          if (
            props?.options?.headerTitle &&
            typeof props.options.headerTitle === 'string'
          ) {
            return props.options.headerTitle;
          }
          if (props?.options?.title) {
            return props.options.title;
          }
          return '';
        };

        const getRight = () => {
          if (
            props?.options?.headerRight &&
            typeof props.options.headerRight === 'function'
          ) {
            // @ts-ignore - React Navigation headerRight function compatibility
            return props.options.headerRight();
          }
        };

        const getLeft = () => {
          if (
            props?.options?.headerLeft &&
            typeof props.options.headerLeft === 'function'
          ) {
            // @ts-ignore - React Navigation headerLeft function compatibility
            return props.options.headerLeft();
          }
        };

        return (
          <NavBar
            // @ts-ignore - NavBar title accepts ReactNode but types are strict
            title={getTitle()}
            backgroundColor={ColorsV2.neutralWhite}
            onGoBack={() => navigation.goBack()}
            isShadow={true}
            right={getRight()}
            left={getLeft()}
          />
        );
      },
    }),
    [],
  );

  return (
    <Stack.Navigator
      screenOptions={screenOptions}
      initialRouteName={
        isFirstOpen ? RouteName.IntroService : RouteName.ChooseService
      }
    >
      <Stack.Screen
        name={RouteName.IntroService}
        component={IntroService}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={RouteName.ChooseService}
        component={ChooseService}
        options={{
          title: t('LIST_OF_LOCATIONS'),
        }}
      />
      <Stack.Screen
        name={RouteName.WorkingDescriptionProgress}
        component={WorkingDescriptionProgress}
        options={{
          title: t('MOVING_PROGRESS'),
        }}
      />
      <Stack.Screen
        name={RouteName.StandardPackagingProcess}
        component={StandardPackagingProcessScreen}
        options={{
          title: t('STANDARD_PACKAGING_PROCESS'),
        }}
      />
      <Stack.Screen
        name={RouteName.ConfirmAndPayment}
        component={ConfirmAndPayment}
        options={{
          title: t('PT2_CONFIRM_HEADER_TITLE'),
        }}
      />
      <Stack.Screen
        name={RouteName.PostTaskSuccess}
        component={PostTaskSuccess}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
