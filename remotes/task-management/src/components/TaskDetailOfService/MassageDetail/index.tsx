import React from 'react';
import {
  BlockView,
  CText,
  getTextWithLocale,
  IconImage,
  ITaskDetail,
  Spacing,
} from '@btaskee/design-system';
import { get, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { icOptionMsg } from '@images';

import { styles } from './styles';

export const MassageDetail = ({ dataTask }: { dataTask: ITaskDetail }) => {
  const { t } = useI18n();

  const massagePackages = get(dataTask, 'detailMassage.packages', []);

  if (isEmpty(massagePackages)) {
    return null;
  }

  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <IconImage
        style={styles.iconStyle}
        source={icOptionMsg}
      />
      <BlockView style={styles.wrapTextDetail}>
        {massagePackages?.map((data: any, indexItem) => {
          const text = `${getTextWithLocale(data?.title)} - ${getTextWithLocale(
            data?.option?.text,
          )}`;
          const bottom =
            indexItem === massagePackages?.length - 1 ? 0 : Spacing.SPACE_04;
          return (
            <BlockView
              testID={`massage_${data?.name}`}
              key={`${getTextWithLocale(data?.title)}-${indexItem}`}
              row
              margin={{ left: Spacing.SPACE_16, bottom }}
            >
              <CText>{t('MASSAGE.DOT_PREFIX')}</CText>
              <BlockView>
                <CText>
                  {text}
                  {data?.taskerGender
                    ? ` - ${t(`MASSAGE.${data?.taskerGender}`)}`
                    : null}
                </CText>
              </BlockView>
            </BlockView>
          );
        })}
      </BlockView>
    </BlockView>
  );
};
