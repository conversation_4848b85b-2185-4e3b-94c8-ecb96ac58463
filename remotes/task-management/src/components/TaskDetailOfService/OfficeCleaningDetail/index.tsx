import React from 'react';
import {
  BlockView,
  CText,
  IconImage,
  ISO_CODE,
  ITaskDetail,
} from '@btaskee/design-system';
import { get, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { icArea } from '@images';

import { styles } from './styles';

export const OfficeCleaningDetail = ({
  dataTask,
}: {
  dataTask: ITaskDetail;
}) => {
  const { t } = useI18n();
  const isoCode = dataTask?.isoCode;

  if (isEmpty(dataTask?.detailOfficeCleaning)) {
    return null;
  }

  const numberOfTaskers = get(
    dataTask,
    'detailOfficeCleaning.numberOfTaskers',
    null,
  );
  const area = get(dataTask, 'detailOfficeCleaning.area', null);

  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <IconImage
        style={styles.iconStyle}
        source={icArea}
      />
      <BlockView style={styles.wrapTextDetail}>
        <CText
          testID={'areaNumber'}
          numberOfLines={2}
          style={styles.txtItemDetailTask}
        >
          {t(
            isoCode === ISO_CODE.TH
              ? 'AREA_ITEM_PEOPLE_NUMBER_TH'
              : 'AREA_ITEM_PEOPLE_NUMBER',
            {
              t1: area,
              t2: numberOfTaskers,
            },
          )}
        </CText>
      </BlockView>
    </BlockView>
  );
};
