import React from 'react';
import {
  BlockView,
  CText,
  FastImage,
  getTextWithLocale,
  ITaskDetail,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { iconPerson } from '@images';

import { styles } from './styles';

export const ChildCareDetail = ({ dataTask }: { dataTask: ITaskDetail }) => {
  const { t } = useI18n();

  const detailChildCare = dataTask?.detailChildCare;

  if (isEmpty(detailChildCare)) {
    return null;
  }

  const _getAgeTypeText = () => {
    if (
      !detailChildCare?.detailChildren ||
      detailChildCare?.detailChildren?.length === 0
    ) {
      return null;
    }
    return detailChildCare.detailChildren
      .map((item: any) => getTextWithLocale(item.age.text))
      .join('\n');
  };

  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <FastImage
        style={styles.iconStyle}
        source={iconPerson}
      />
      <BlockView style={styles.wrapTextDetail}>
        <CText
          testID={'areaNumber'}
          numberOfLines={2}
          style={styles.txtItemDetailTask}
        >
          {t('CHILD_CARE_STEP_4_AMOUNT_CHILD')}:{' '}
          {t('CHILD_CARE_NUM', { t: detailChildCare?.numberOfChildren })}/
          {_getAgeTypeText()}
        </CText>
      </BlockView>
    </BlockView>
  );
};
