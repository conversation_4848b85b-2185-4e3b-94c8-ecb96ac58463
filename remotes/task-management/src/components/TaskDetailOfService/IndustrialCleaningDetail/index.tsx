import React from 'react';
import {
  BlockView,
  CText,
  formatMoney,
  getTextWithLocale,
  IconImage,
  ITaskDetail,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { icAreaCarpet } from '@images';

import { styles } from './styles';

export const IndustrialCleaningDetail = ({
  dataTask,
}: {
  dataTask: ITaskDetail;
}) => {
  const { t } = useI18n();
  const industrialType = dataTask?.detailIndustrialCleaning?.homeType?.type;

  if (isEmpty(industrialType)) {
    return null;
  }

  // Trường hợp diện tích cố định
  let textArea = `${getTextWithLocale(
    industrialType?.text,
  )} - ${getTextWithLocale(industrialType?.area?.text)}`;
  // Trường hợp diện tích không cố định
  if (industrialType?.area?.customArea) {
    textArea = `${getTextWithLocale(industrialType?.text)} - ${t(
      'DISINFECTION_SERVICE_AREA_ITEM',
      {
        t: formatMoney(industrialType?.area?.customArea),
      },
    )}`;
  }

  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <IconImage
        style={styles.iconStyle}
        source={icAreaCarpet}
      />
      <BlockView style={styles.wrapTextDetail}>
        <CText
          testID={'areaNumber'}
          numberOfLines={2}
          style={styles.txtItemDetailTask}
        >
          {textArea}
        </CText>
      </BlockView>
    </BlockView>
  );
};
