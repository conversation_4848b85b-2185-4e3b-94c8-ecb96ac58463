import React from 'react';
import {
  BlockView,
  CText,
  IconImage,
  ITaskDetail,
} from '@btaskee/design-system';
import { get } from 'lodash-es';

import { icMenu } from '@images';

import { styles } from './styles';

export const HomeCookingDetail = ({ dataTask }: { dataTask: ITaskDetail }) => {
  const dishList = get(dataTask, 'cookingDetail.dishDetail', []) || [];

  if (dishList.length === 0) {
    return null;
  }

  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <IconImage
        style={styles.iconStyle}
        source={icMenu}
      />
      <BlockView style={styles.wrapTextDetail}>
        {dishList.map((data: any, index: number) => {
          return (
            <CText
              key={index}
              numberOfLines={2}
              style={styles.itemDetailTask}
            >
              - {data?.name}
            </CText>
          );
        })}
      </BlockView>
    </BlockView>
  );
};
