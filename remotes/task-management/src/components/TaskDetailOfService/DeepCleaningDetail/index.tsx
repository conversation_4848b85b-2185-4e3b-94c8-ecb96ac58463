import React from 'react';
import { BlockView, CText, Icon, ITaskDetail } from '@btaskee/design-system';
import { get } from 'lodash-es';

import { useI18n } from '@hooks';

import { styles } from './styles';

export const DeepCleaningDetail = ({ dataTask }: { dataTask: ITaskDetail }) => {
  const { t } = useI18n();
  const numberOfTaskersDeepCleaning = get(
    dataTask,
    'detailDeepCleaning.numberOfTaskersDeepCleaning',
    null,
  );
  const areaDeepCleaning = get(
    dataTask,
    'detailDeepCleaning.areaDeepCleaning',
    null,
  );

  if (!areaDeepCleaning) {
    return null;
  }

  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <Icon
        style={styles.iconStyle}
        name={'icTaskerNumber'}
      />
      <BlockView style={styles.wrapTextDetail}>
        <CText
          testID={'areaNumber'}
          numberOfLines={2}
          style={styles.txtItemDetailTask}
        >
          {t('AREA_ITEM_PEOPLE_NUMBER', {
            t1: areaDeepCleaning,
            t2: numberOfTaskersDeepCleaning,
          })}
        </CText>
      </BlockView>
    </BlockView>
  );
};
