import React from 'react';
import {
  BlockView,
  CText,
  FastImage,
  Icon,
  ITaskDetail,
} from '@btaskee/design-system';
import { get, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { icAirConditioner } from '@images';

import { styles } from './styles';

export const AirConditionerDetail = ({
  dataTask,
}: {
  dataTask: ITaskDetail;
}) => {
  const { t } = useI18n();
  const detail = get(
    dataTask,
    'detailAirConditioner',
    get(dataTask, 'detail', []),
  );

  if (isEmpty(detail)) {
    return null;
  }
  let count = 0;
  detail.forEach((data: any) => (count += data.quantity));
  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <FastImage
        style={styles.iconStyle}
        source={icAirConditioner}
      />
      <BlockView style={styles.wrapTextDetail}>
        <CText
          testID={'areaNumber'}
          numberOfLines={2}
          style={styles.txtItemDetailTask}
        >
          {t('AMOUNT_AIR_CONDITIONER', { t: count })}
        </CText>
      </BlockView>
    </BlockView>
  );
};
