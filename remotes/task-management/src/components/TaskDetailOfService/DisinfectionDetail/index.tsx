import React from 'react';
import {
  BlockView,
  CText,
  IconImage,
  ITaskDetail,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { icArea } from '@images';

import { styles } from './styles';

export const DisinfectionDetail = ({ dataTask }: { dataTask: ITaskDetail }) => {
  const { t } = useI18n();

  const customArea = dataTask?.disinfectionDetail?.customArea;
  const area = dataTask?.disinfectionDetail?.area;
  let textArea = t('DISINFECTION_SERVICE_AREA_ITEM', { t: customArea });
  if (area?.from && area?.to) {
    textArea = `${t('DISINFECTION_SERVICE_AREA_ITEM', {
      t: area?.from,
    })} - ${t('DISINFECTION_SERVICE_AREA_ITEM', { t: area?.to })}`;
  }

  if (isEmpty(dataTask?.disinfectionDetail)) {
    return null;
  }

  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <IconImage
        style={styles.iconStyle}
        source={icArea}
      />
      <BlockView style={styles.wrapTextDetail}>
        <CText
          testID={'areaNumber'}
          numberOfLines={2}
          style={styles.txtItemDetailTask}
        >
          {t('DISINFECTION_SERVICE_AREA_1', { t: textArea })}
        </CText>
      </BlockView>
    </BlockView>
  );
};
