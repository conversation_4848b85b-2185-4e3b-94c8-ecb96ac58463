import React, { useMemo } from 'react';
import {
  BlockView,
  ConditionView,
  CText,
  getTextWithLocale,
  IconImage,
  ITaskDetail,
} from '@btaskee/design-system';
import { TYPE_WASHING_MACHINE } from '@constants';
import { countBy, get, isEmpty } from 'lodash-es';

import { iconWMLine } from '@images';

import { styles } from './styles';

export const WashingMachineDetail = ({
  dataTask,
}: {
  dataTask: ITaskDetail;
}) => {
  const detail = get(dataTask, 'detailWashingMachine', []) as any[];

  const top = useMemo(() => {
    return {
      count: countBy(detail, 'name')[TYPE_WASHING_MACHINE.topLoading] || 0,
      text: detail.find(
        (item) => item?.name === TYPE_WASHING_MACHINE.topLoading,
      )?.text,
    };
  }, [detail]);

  const front = useMemo(() => {
    return {
      count: countBy(detail, 'name')[TYPE_WASHING_MACHINE.frontLoading] || 0,
      text: detail.find(
        (item) => item?.name === TYPE_WASHING_MACHINE.frontLoading,
      )?.text,
    };
  }, []);

  if (isEmpty(detail)) {
    return null;
  }

  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <IconImage
        style={styles.iconStyle}
        source={iconWMLine}
      />
      <BlockView style={styles.wrapTextDetail}>
        <ConditionView
          condition={Boolean(top.count)}
          viewTrue={
            <BlockView row>
              <CText
                numberOfLines={2}
                style={styles.itemDetailTask}
                testID="txtNumberTopLoading"
              >
                {getTextWithLocale(top.text)}
              </CText>
              <CText
                bold
                style={styles.txtNumber}
                testID="numberTopLoading"
              >
                x{top.count}
              </CText>
            </BlockView>
          }
        />
        <ConditionView
          condition={Boolean(front.count)}
          viewTrue={
            <BlockView row>
              <CText
                numberOfLines={2}
                style={styles.itemDetailTask}
                testID="txtNumberFrontLoading"
              >
                {getTextWithLocale(front.text)}
              </CText>
              <CText
                bold
                style={styles.txtNumber}
                testID="numberFrontLoading"
              >
                x{front.count}
              </CText>
            </BlockView>
          }
        />
      </BlockView>
    </BlockView>
  );
};
