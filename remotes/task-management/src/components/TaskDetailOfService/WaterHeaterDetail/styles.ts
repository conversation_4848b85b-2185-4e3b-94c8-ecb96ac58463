import { StyleSheet } from 'react-native';
import { ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  wrapDetailTaskItem: {
    flexDirection: 'row',
    marginBottom: Spacing.SPACE_08,
  },
  txtItemDetailTask: {
    marginHorizontal: Spacing.SPACE_12,
    color: ColorsV2.neutral900,
  },
  iconStyle: {
    width: Spacing.SPACE_24,
    height: Spacing.SPACE_24,
  },
  wrapTextDetail: {
    justifyContent: 'center',
  },
  itemDetailTask: {
    marginHorizontal: Spacing.SPACE_16,
    color: ColorsV2.neutral900,
  },
  txtNumber: {
    color: ColorsV2.green500,
  },
});
