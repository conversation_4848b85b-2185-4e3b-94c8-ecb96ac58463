import React, { useMemo } from 'react';
import {
  BlockView,
  CText,
  IconImage,
  ITaskDetail,
} from '@btaskee/design-system';
import { get, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { icWaterHeater } from '@images';

import { styles } from './styles';

export const WaterHeaterDetail = ({ dataTask }: { dataTask: ITaskDetail }) => {
  const { t } = useI18n();
  const detail = get(dataTask, 'detailWaterHeater', []) as any[];

  const numberOfWH = useMemo(() => {
    let total = 0;
    detail.forEach((item) => {
      total += item.quantity;
    });
    return total;
  }, [detail]);

  if (isEmpty(detail)) {
    return null;
  }

  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <IconImage
        style={styles.iconStyle}
        source={icWaterHeater}
      />
      <BlockView style={styles.wrapTextDetail}>
        <BlockView row>
          <CText
            numberOfLines={2}
            style={styles.itemDetailTask}
          >
            {t('WATER_HEATER.SERVICE_NAME')}
          </CText>
          <CText
            testID={`txtQuantityWH_${numberOfWH}`}
            bold
            style={styles.txtNumber}
          >
            {t('WATER_HEATER.QUANTITY_OPTION', { t: numberOfWH })}
          </CText>
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
