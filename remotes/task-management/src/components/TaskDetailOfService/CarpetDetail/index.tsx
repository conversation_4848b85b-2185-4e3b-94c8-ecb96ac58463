import React from 'react';
import {
  BlockView,
  CText,
  IconImage,
  ITaskDetail,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { icArea } from '@images';

import { styles } from './styles';

export const CarpetDetail = ({ dataTask }: { dataTask: ITaskDetail }) => {
  const { t } = useI18n();

  if (isEmpty(dataTask?.detailCarpet)) {
    return null;
  }

  const customArea = dataTask?.detailCarpet?.customArea;
  const area = dataTask?.detailCarpet?.area;

  let textArea = t('DISINFECTION_SERVICE_AREA_ITEM', { t: customArea });
  if (area?.from && area?.to) {
    textArea = `${t('DISINFECTION_SERVICE_AREA_ITEM', { t: area?.from })} - ${t(
      'DISINFECTION_SERVICE_AREA_ITEM',
      {
        t: area?.to,
      },
    )}`;
  }

  return (
    <BlockView style={styles.wrapDetailTaskItem}>
      <IconImage
        style={styles.iconStyle}
        source={icArea}
      />
      <BlockView style={styles.wrapTextDetail}>
        <CText
          testID={'areaNumber'}
          numberOfLines={2}
          style={styles.txtItemDetailTask}
        >
          {t('DISINFECTION_SERVICE_AREA_1', { t: textArea })}
        </CText>
      </BlockView>
    </BlockView>
  );
};
