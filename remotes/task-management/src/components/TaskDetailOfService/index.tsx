import React from 'react';
import { ITaskDetail, SERVICES } from '@btaskee/design-system';

import { AirConditionerDetail } from './AirConditionerDetail';
import { CarpetDetail } from './CarpetDetail';
import { ChildCareDetail } from './ChildCareDetail';
import { DeepCleaningDetail } from './DeepCleaningDetail';
import { DisinfectionDetail } from './DisinfectionDetail';
import { HomeCookingDetail } from './HomeCookingDetail';
import { IndustrialCleaningDetail } from './IndustrialCleaningDetail';
import { MassageDetail } from './MassageDetail';
import { WashingMachineDetail } from './WashingMachineDetail';
import { WaterHeaterDetail } from './WaterHeaterDetail';

interface PostTaskStep2Components {
  [key: string]: React.ComponentType<any>;
}

const listStep2: PostTaskStep2Components = {};
// add Step 3 component by service name
listStep2[SERVICES.DEEP_CLEANING] = DeepCleaningDetail;
listStep2[SERVICES.AIR_CONDITIONER] = AirConditionerDetail;
listStep2[SERVICES.CHILD_CARE] = ChildCareDetail;
listStep2[SERVICES.HOME_COOKING] = HomeCookingDetail;
listStep2[SERVICES.DISINFECTION_SERVICE] = DisinfectionDetail;
listStep2[SERVICES.INDUSTRIAL_CLEANING] = IndustrialCleaningDetail;
listStep2[SERVICES.MASSAGE] = MassageDetail;
listStep2[SERVICES.OFFICE_CARPET_CLEANING] = CarpetDetail;
listStep2[SERVICES.WASHING_MACHINE] = WashingMachineDetail;
listStep2[SERVICES.WATER_HEATER] = WaterHeaterDetail;

export const TaskDetailOfService = ({
  dataTask,
}: {
  dataTask: ITaskDetail;
}) => {
  // get Step 3 component by service name
  const TaskDetailOfServices = listStep2[dataTask?.service?.name as SERVICES];

  if (!TaskDetailOfServices) {
    return null;
  }

  return <TaskDetailOfServices dataTask={dataTask} />;
};
