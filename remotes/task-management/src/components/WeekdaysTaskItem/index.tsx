/**
 * @Filename: components/task-item/components/weekdays-task-item.js
 * @Description:
 * @CreatedAt: 22/02/2021
 * @Author: HuuToan
 * @UpdatedAt: 22/02/2021
 * @UpdatedBy: HuuToan
 **/

import React from 'react';
import {
  BlockView,
  capitalize,
  CText,
  DateTimeHelpers,
  IconImage,
  ITaskDetail,
  TypeFormatDate,
} from '@btaskee/design-system';

import { icClockSchedule } from '@images';

import { styles } from './styles';

export const WeekdaysTaskItem = ({
  dataTask,
  index,
}: {
  dataTask: ITaskDetail;
  index: number;
}) => {
  let strWeekdays = null;
  const timezone = dataTask.timezone;

  const weekdays = dataTask.weekdays || dataTask.weeklyRepeater;

  if (weekdays) {
    strWeekdays = weekdays
      .map((data: number) =>
        capitalize(
          DateTimeHelpers.formatToString({
            timezone: timezone,
            date: DateTimeHelpers.toDayTz({
              timezone: timezone,
            }).day(data),
            typeFormat: TypeFormatDate.DayAbbreviatedFull,
          }),
        ),
      )
      .join(' - ');
  }

  if (!strWeekdays) return null;
  return (
    <BlockView style={styles.wrapItemDetailTask}>
      <IconImage
        source={icClockSchedule}
        style={styles.imageIcon}
      />
      <BlockView center>
        <CText
          testID={`weekdays${index}`}
          style={styles.txtItemDetailTask}
        >
          {strWeekdays}
        </CText>
      </BlockView>
    </BlockView>
  );
};
