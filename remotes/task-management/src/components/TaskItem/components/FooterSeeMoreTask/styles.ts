import { StyleSheet } from 'react-native';
import { ColorsV2 } from '@btaskee/design-system';

export default StyleSheet.create({
  wrap_footer: {
    width: '90%',
    height: 10,
    alignSelf: 'center',
    borderColor: ColorsV2.orange500,
    borderBottomWidth: 1,
    borderLeftColor: ColorsV2.orange500,
    borderLeftWidth: 1,
    borderRightColor: ColorsV2.orange500,
    borderRightWidth: 1,
    borderBottomEndRadius: 30,
    borderBottomStartRadius: 30,
    backgroundColor: ColorsV2.neutralWhite,
  },
  backgroundSchedule: {
    backgroundColor: ColorsV2.green50,
    borderRightColor: ColorsV2.green500,
    borderLeftColor: ColorsV2.green500,
    borderColor: ColorsV2.green500,
  },
  backgroundSubscription: {
    backgroundColor: ColorsV2.orange50,
    borderRightColor: ColorsV2.orange500,
    borderLeftColor: ColorsV2.orange500,
    borderColor: ColorsV2.orange500,
  },
});
