import { StyleSheet } from 'react-native';
import { ColorsV2, <PERSON><PERSON><PERSON>elper, Spacing } from '@btaskee/design-system';

const { WIDTH } = DeviceHelper.WINDOW;

export default StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.SPACE_20,
    backgroundColor: ColorsV2.green100,
    paddingVertical: Spacing.SPACE_16,
  },
  backgroundSubscription: {
    backgroundColor: ColorsV2.orange50,
  },
  wrap_itemDetailTask: {
    flexDirection: 'row',
  },
  txt_itemDetailTask: {
    marginHorizontal: Spacing.SPACE_08,
  },
  btnSeeMore: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  imageIcon: {
    width: Spacing.SPACE_24,
    height: Spacing.SPACE_24,
  },
  iconArrowDown: {
    width: Spacing.SPACE_20,
    height: Spacing.SPACE_20,
    marginLeft: Spacing.SPACE_04,
  },
  wrap_footer: {
    width: '90%',
    height: 10,
    alignSelf: 'center',
    borderColor: ColorsV2.orange500,
    borderBottomWidth: 1,
    borderLeftColor: ColorsV2.orange500,
    borderLeftWidth: 1,
    borderRightColor: ColorsV2.orange500,
    borderRightWidth: 1,
    borderBottomEndRadius: 30,
    borderBottomStartRadius: 30,
    backgroundColor: ColorsV2.neutralWhite,
  },
  txt_TitleTaskSchedule: {
    marginHorizontal: Spacing.SPACE_16,
    marginVertical: Spacing.SPACE_16,
  },
  wrap_detailTask: {
    borderColor: ColorsV2.neutral100,
    paddingHorizontal: Spacing.SPACE_08,
    paddingTop: Spacing.SPACE_08,
    borderTopWidth: 1,
    borderTopColor: ColorsV2.neutral100,
  },
  wrap_avatar: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.SPACE_08,
    paddingVertical: Spacing.SPACE_04,
    justifyContent: 'space-between',
  },
  txt_taskerName: {
    marginHorizontal: Spacing.SPACE_08,
  },
  scrollListSchedule: {
    height: DeviceHelper.WINDOW.HEIGHT * 0.4,
  },
  dotStyle: {
    width: 5,
    height: 5,
    borderRadius: 5,
    marginHorizontal: Spacing.SPACE_04,
    backgroundColor: ColorsV2.orange100,
  },
  dotStyleActive: {
    width: 10,
    height: 10,
    backgroundColor: ColorsV2.orange500,
  },
  paginationContainerStyle: {
    paddingVertical: Spacing.SPACE_16,
    paddingBottom: 0,
  },
  btnScheduleDetail: {
    flexDirection: 'row',
  },
  boxContainerItem: {
    borderRadius: Spacing.SPACE_16,
    backgroundColor: ColorsV2.neutralWhite,
  },
  containerStatus: {
    borderRadius: 20,
    paddingHorizontal: 10,
    paddingVertical: 3,
    justifyContent: 'flex-start',
  },
  txtStatus: {
    maxWidth: Math.round(WIDTH / 2.6),
  },
});
