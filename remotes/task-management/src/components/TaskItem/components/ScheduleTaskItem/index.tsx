/**
 * @Description:
 * @CreatedAt: 22/02/2021
 * @Author: Hu<PERSON><PERSON><PERSON>
 * @UpdatedAt: 22/02/2021
 * @UpdatedBy: HuuToan
 **/
import React, { useMemo, useState } from 'react';
import { Dimensions, TouchableOpacity } from 'react-native';
import * as Animatable from 'react-native-animatable';
import Carousel from 'react-native-reanimated-carousel';
import {
  Avatar,
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  HitSlop,
  Icon,
  NavigationService,
  RouteName,
  Spacing,
  STATUS_OF_PREPAYMENT,
  TASK_STATUS,
} from '@btaskee/design-system';
import { getColorStatusOfTask, getStatusTask } from '@helper';
import { isEmpty } from 'lodash-es';

import { useAppNavigation, useI18n } from '@hooks';
import { RouteName as RouteNameTaskManagement } from '@navigation/RouteName';

import { DurationTaskItem } from '../../../DurationTaskItem';
import { DateTimeTaskItem } from '../DateTimeTaskItem';
import { PriceTaskItem } from '../PriceTaskItem';
import styles from './styles';

const { width } = Dimensions.get('window');
const screenWidthCarousel = width - 32;

const RenderTaskScheduleItem = ({ dataTask }: any) => {
  const { t } = useI18n();
  const colorTaskStatus = getColorStatusOfTask(dataTask?.status) || {};

  const onPressTaskItem = () => {
    // Các trường hợp chưa thanh toán thành công thì đi tới trang thanh toán lại
    if (
      dataTask?.isPrepayTask &&
      dataTask?.payment?.status !== STATUS_OF_PREPAYMENT.paid
    ) {
      return NavigationService.navigate('RepayDetail', {
        taskId: dataTask._id,
        dataTask: dataTask,
      });
    }
    return NavigationService.navigate(RouteName.TaskManagement, {
      screen: RouteNameTaskManagement.TaskDetail,
      params: {
        taskId: dataTask._id!,
      },
    });
  };

  const getTextAcceptedTask = () => {
    let text = 'WAITING_TASKER_ACCEPT';
    switch (dataTask?.status) {
      case TASK_STATUS.WAITING_ASKER_CONFIRMATION:
        text = 'LABEL_WAITING_ASKER_CONFIRMATION';
        break;
      case TASK_STATUS.CANCELED:
        text = 'NO_TASKER_ACCEPT';
        break;

      default:
        break;
    }
    return t(text);
  };

  const textWithAcceptedTask =
    dataTask?.acceptedTasker?.[0]?.name || getTextAcceptedTask();

  return (
    <TouchableOpacity
      onPress={onPressTaskItem}
      style={[styles.boxContainerItem]}
    >
      <BlockView style={styles.wrap_avatar}>
        <BlockView
          row
          center
          flex
        >
          <Avatar
            size={40}
            avatar={dataTask?.acceptedTasker?.[0]?.avatar}
          />
          <BlockView flex>
            <CText
              numberOfLines={2}
              style={styles.txt_taskerName}
            >
              {textWithAcceptedTask}
            </CText>
          </BlockView>
        </BlockView>
        <BlockView center>
          <BlockView
            style={[
              styles.containerStatus,
              { backgroundColor: colorTaskStatus.backgroundColor },
            ]}
          >
            <CText
              numberOfLines={2}
              style={[{ color: colorTaskStatus.color }, styles.txtStatus]}
            >
              {t(getStatusTask({ status: dataTask?.status }))}
            </CText>
          </BlockView>
        </BlockView>
      </BlockView>
      <BlockView style={styles.wrap_detailTask}>
        <DateTimeTaskItem
          // fromScreen={TAB_ACTIVITY.TabUpcoming}
          dataTask={dataTask}
        />
        <DurationTaskItem
          // fromScreen={TAB_ACTIVITY.TabUpcoming}
          dataTask={dataTask}
        />
        <PriceTaskItem dataTask={dataTask} />
      </BlockView>
    </TouchableOpacity>
  );
};

/**
 * Simple dot indicator component for carousel pagination
 */
const DotIndicator = ({
  dotsLength,
  activeIndex,
}: {
  dotsLength: number;
  activeIndex: number;
}) => {
  if (dotsLength <= 1) return null;

  return (
    <BlockView
      row
      center
      style={styles.paginationContainerStyle}
    >
      {Array.from({ length: dotsLength }).map((_, index) => (
        <BlockView
          key={index}
          style={[
            styles.dotStyle,
            index === activeIndex && styles.dotStyleActive,
          ]}
        />
      ))}
    </BlockView>
  );
};

export const ScheduleTaskItem = ({
  handleShowMore,
  showMore,
  dataTask,
}: {
  handleShowMore: () => void;
  showMore: boolean;
  dataTask: any;
}) => {
  const { t } = useI18n();
  const [activeSlide, setActiveSlide] = useState(0);
  const navigation = useAppNavigation();

  const listTaskSchedule = dataTask?.relatedTasks?.tasks || [];
  const scheduleId = dataTask?.scheduleId;
  const subscriptionId = dataTask?.subscriptionId;

  const colorOfType = useMemo(() => {
    if (subscriptionId) {
      return ColorsV2.orange500;
    }

    return ColorsV2.green500;
  }, [subscriptionId]);

  const titleOfSchedule = useMemo(() => {
    if (subscriptionId) {
      if (dataTask?.isEco) {
        return t('SUBSCRIPTION.ECO_SUBSCRIPTION');
      }
      return t('SERVICE_BY_SUBSCRIPTION');
    }
    return t('POST_TASK_CHECKBOX_REPEAT');
  }, [subscriptionId, t, dataTask?.isEco]);

  const goToScheduleDetail = () => {
    // if (subscriptionId) {
    //   // handle subscription
    //   navigation.navigate(RouteName.SubScriptionWorkingSchedule, {
    //     subscriptionId,
    //   });
    // }
    // if (scheduleId) {
    //   navigation.navigate(RouteName.TaskScheduleUpdate, { scheduleId });
    // }
  };

  return (
    <>
      <BlockView
        style={[
          styles.container,
          subscriptionId ? styles.backgroundSubscription : {},
        ]}
      >
        <BlockView style={styles.wrap_itemDetailTask}>
          <BlockView
            jBetween
            row
            flex
          >
            <TouchableOpacity
              testID="scheduleBtn"
              style={styles.btnScheduleDetail}
              onPress={() => goToScheduleDetail()}
            >
              <Icon
                name={subscriptionId ? 'icSubscription' : 'icSchedule'}
                style={styles.imageIcon}
              />
              <BlockView justify="center">
                <CText
                  bold
                  underline
                  color={colorOfType}
                  style={styles.txt_itemDetailTask}
                >
                  {titleOfSchedule}
                </CText>
              </BlockView>
            </TouchableOpacity>

            <ConditionView
              condition={!isEmpty(listTaskSchedule)}
              viewTrue={
                <TouchableOpacity
                  hitSlop={HitSlop.MEDIUM}
                  onPress={() => handleShowMore()}
                >
                  <ConditionView
                    condition={Boolean(showMore)}
                    viewFalse={
                      <BlockView
                        row
                        center
                      >
                        <CText color={colorOfType}>{t('SEE_MORE')}</CText>
                        <Icon
                          color={colorOfType}
                          name={'icArrowDown'}
                          style={styles.iconArrowDown}
                        />
                      </BlockView>
                    }
                    viewTrue={
                      <BlockView
                        row
                        center
                      >
                        <CText color={colorOfType}>{t('SEE_LESS')}</CText>
                        <Icon
                          color={colorOfType}
                          name={'icArrowUp'}
                          style={styles.iconArrowDown}
                        />
                      </BlockView>
                    }
                  />
                </TouchableOpacity>
              }
            />
          </BlockView>
        </BlockView>
      </BlockView>

      <ConditionView
        condition={Boolean(showMore) && !isEmpty(listTaskSchedule)}
        viewTrue={
          <BlockView
            backgroundColor={ColorsV2.neutralBackground}
            padding={{ bottom: Spacing.SPACE_16 }}
          >
            <BlockView
              jBetween
              row
              flex
            >
              <CText
                bold
                size={FontSizes.SIZE_14}
                style={styles.txt_TitleTaskSchedule}
              >
                {t('TASK_SCHEDULE.HAS_SCHEDULED')}
              </CText>
              <CText
                testID="countTaskSchedule"
                bold
                size={FontSizes.SIZE_14}
                style={styles.txt_TitleTaskSchedule}
              >
                {listTaskSchedule?.length}
              </CText>
            </BlockView>

            <ConditionView
              condition={listTaskSchedule?.length === 1}
              viewTrue={
                <RenderTaskScheduleItem
                  dataTask={listTaskSchedule[0]}
                  index={0}
                  isLastItem={true}
                />
              }
              viewFalse={
                <Animatable.View
                  useNativeDriver
                  duration={300}
                  animation="fadeIn"
                  delay={100}
                >
                  <BlockView row>
                    <Carousel
                      loop={true}
                      width={screenWidthCarousel}
                      height={160}
                      data={listTaskSchedule}
                      pagingEnabled={true}
                      mode="parallax"
                      modeConfig={{
                        parallaxScrollingScale: 0.9,
                        parallaxScrollingOffset: 50,
                      }}
                      renderItem={({ item }) => (
                        <RenderTaskScheduleItem dataTask={item} />
                      )}
                      defaultIndex={0}
                      onSnapToItem={(index: number) => {
                        setActiveSlide(index);
                      }}
                    />
                  </BlockView>
                  <DotIndicator
                    dotsLength={listTaskSchedule?.length}
                    activeIndex={activeSlide}
                  />
                </Animatable.View>
              }
            />
          </BlockView>
        }
      />
    </>
  );
};
