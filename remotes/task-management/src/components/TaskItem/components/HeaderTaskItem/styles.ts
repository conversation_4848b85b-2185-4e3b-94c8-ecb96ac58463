import { StyleSheet } from 'react-native';
import { ColorsV2, FontSizes, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  wrap_header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_08,
  },
  wrap_serviceName: {
    flex: 1,
    paddingRight: Spacing.SPACE_04,
  },
  txt_labelPosted: {
    color: ColorsV2.neutral500,
    fontSize: FontSizes.SIZE_14,
    marginTop: Spacing.SPACE_04,
  },
});
