import {
  BlockView,
  CText,
  DateTimeHelpers,
  IDate,
  TypeFormatDate
} from '@btaskee/design-system';
import { get } from 'lodash-es';
import React from 'react';

import { useI18n } from '@hooks';

import { StatusTask } from '../../../StatusTask';
import HeaderServiceName from '../../../HeaderServiceName';
import { styles } from './styles';

export const HeaderTaskItem = ({
  dataTask,
  isPaymentShowFailed,
  isTaskHaveLeader,
}: {
  dataTask: any;
  isPaymentShowFailed?: boolean;
  isTaskHaveLeader?: boolean;
}) => {
  const createdAt = get(dataTask, 'createdAt', null);
  const { t } = useI18n();


  const formatDateFromNow = (date?: IDate) => {
    if (!date) return '';

    const timezone = DateTimeHelpers.getTzDevice();
    const diffMinute = Math.round(
      DateTimeHelpers.diffDate({
        secondDate: date,
        timezone,
        unit: 'minute',
      }) || 0,
    );
    if (diffMinute < 1) {
      return t('JUST_NOW');
    } else if (diffMinute < 60) {
      return t('MINUTES_AGO', {
        count: diffMinute,
      });
    } else if (diffMinute < 1440) {
      const hours = Math.round(diffMinute / 60);
      return t('HOUR_AGO', {
        count: hours,
      });
    } else {
      return DateTimeHelpers.formatToString({
        date,
        timezone,
        typeFormat: TypeFormatDate.DateShort,
      });
    }
  };

  return (
    <BlockView style={styles.wrap_header}>
      <BlockView
        flex
        style={[styles.wrap_serviceName]}
      >
        <BlockView>
          <HeaderServiceName dataTask={dataTask} />

          {createdAt ? (
            <CText style={styles.txt_labelPosted}>
              {t('TASK_POSTED_AT', { t: formatDateFromNow(createdAt) })}
            </CText>
          ) : null}
        </BlockView>
      </BlockView>

      <StatusTask dataTask={dataTask} isPaymentShowFailed={isPaymentShowFailed} isTaskHaveLeader={isTaskHaveLeader}/>
    </BlockView>
  );
};
