/**
 * @Filename: components/task-item/components/price-task-item.js
 * @Description:
 * @CreatedAt: 22/02/2021
 * @Author: HuuToan
 * @UpdatedAt: 22/02/2021
 * @UpdatedBy: HuuToan
 **/

import React, { useMemo } from 'react';
import {
  BlockView,
  CText,
  getFinalCost,
  Icon,
  ITaskDetail,
  showPriceAndCurrency,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';
import { get } from 'lodash-es';

import { useI18n } from '@hooks';

import { styles } from './styles';

export const PriceTaskItem = ({ dataTask }: { dataTask: ITaskDetail }) => {
  const { t } = useI18n();

  const price = getFinalCost(dataTask?.costDetail);
  const finalCost = getFinalCost(dataTask?.costDetail);
  const costChange = finalCost - get(dataTask, 'costDetail.finalCost', 0);

  const shouldRenderCostChange = useMemo(() => {
    if (costChange > 0) {
      return (
        <BlockView style={styles.wrapItemDetailTask}>
          <Icon
            name={'icDollar'}
            style={styles.imageIcon}
          />
          <BlockView
            row
            style={styles.blockCostChange}
            testID="costChange"
          >
            <CText
              testID="txtPayForTasker"
              style={styles.txtLeftSpecial}
            >
              {t('COST_CHANGE_FROM_BE')}
            </CText>
            <SizedBox width={Spacing.SPACE_04} />
            <CText
              testID={`lbPrice${dataTask?.description}`}
              style={[styles.txtItemDetailTask, styles.txtLeftSpecial]}
            >
              {showPriceAndCurrency(costChange)}
            </CText>
          </BlockView>
        </BlockView>
      );
    }
  }, [dataTask]);

  if (!price) {
    return null;
  }

  return (
    <>
      {shouldRenderCostChange}
      <BlockView style={styles.wrapItemDetailTask}>
        <Icon
          name={'icDollar'}
          style={styles.imageIcon}
        />
        <BlockView justify="center">
          <CText
            testID={`lbPrice${dataTask?.description}`}
            style={styles.txtItemDetailTask}
          >
            {showPriceAndCurrency(price)}
          </CText>
        </BlockView>
      </BlockView>
    </>
  );
};
