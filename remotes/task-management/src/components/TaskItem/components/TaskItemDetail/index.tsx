/**
 * @Filename: task-item/deep-cleaning/index.js
 * @Description:
 * @CreatedAt: 23/10/2020
 * @Author: Hong<PERSON>hanh
 **/

import React, { useMemo } from 'react';
import { BlockView, CText, Icon, ITaskDetail } from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { AddressTaskItem } from '../../../AddressTaskItem';
import { DurationTaskItem } from '../../../DurationTaskItem';
import { TaskDetailOfService } from '../../../TaskDetailOfService';
import { WeekdaysTaskItem } from '../../../WeekdaysTaskItem';
import { DateTimeTaskItem } from '../DateTimeTaskItem';
import PaymentStatus from '../PaymentStatus';
import { PriceTaskItem } from '../PriceTaskItem';
import { styles } from './styles';

export const TaskItemDetail = ({
  dataTask,
  fromScreen,
  index,
}: {
  dataTask: ITaskDetail;
  fromScreen: string;
  index?: number;
}) => {
  const { t } = useI18n();

  // Render Premium task
  const shouldRenderPremium = useMemo(() => {
    if (dataTask?.isPremium) {
      return (
        <BlockView
          row
          style={styles.wrapDetailTaskItem}
        >
          <Icon
            name={'icLockPremium'}
            style={styles.iconStyle}
          />
          <BlockView>
            <CText style={styles.txtItemDetailTask}>{t('TASK_PREMIUM')}</CText>
          </BlockView>
        </BlockView>
      );
    }
  }, [dataTask?.isPremium]);

  const description = dataTask?.description;

  return (
    <BlockView
      testID={`${fromScreen}${description}`}
      style={styles.wrapDetailTask}
    >
      {shouldRenderPremium}
      <DateTimeTaskItem dataTask={dataTask} />

      <DurationTaskItem
        dataTask={dataTask}
        index={index}
      />
      <PriceTaskItem dataTask={dataTask} />
      <WeekdaysTaskItem
        dataTask={dataTask}
        index={index ?? 0}
      />
      <PaymentStatus dataTask={dataTask} />
      <AddressTaskItem dataTask={dataTask} />
      <TaskDetailOfService dataTask={dataTask} />
    </BlockView>
  );
};
