import { StyleSheet } from 'react-native';
import { ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  wrapDetailTask: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingTop: Spacing.SPACE_16,
    borderTopColor: ColorsV2.neutral100,
    borderTopWidth: 1,
  },
  wrapDetailTaskItem: {
    flexDirection: 'row',
    marginBottom: Spacing.SPACE_08,
  },
  txtItemDetailTask: {
    marginHorizontal: Spacing.SPACE_12,
    color: ColorsV2.neutral900,
  },
  iconStyle: {
    width: Spacing.SPACE_24,
    height: Spacing.SPACE_24,
  },
});
