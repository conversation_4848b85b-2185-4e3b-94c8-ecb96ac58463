import { StyleSheet } from 'react-native';
import { BorderRadius, ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.SPACE_04,
  },
  containerPaymentFailed: {
    paddingBottom: 0,
    borderColor: ColorsV2.red500,
    overflow: 'hidden',
  },
  containerPaymentCharging: {
    paddingBottom: 0,
    borderColor: ColorsV2.yellow500,
    overflow: 'hidden',
  },

  borderSchedule: {
    borderColor: ColorsV2.green500,
  },
  borderSubscription: {
    borderColor: ColorsV2.orange500,
  },

  wrapContainer: {
    backgroundColor: ColorsV2.neutralWhite,
    paddingTop: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_16,
    borderColor: ColorsV2.neutral100,
    borderWidth: 1,
    overflow: 'hidden',
  },
  wrapContent: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingVertical: Spacing.SPACE_08,
    paddingTop: Spacing.SPACE_12,
  },
});
