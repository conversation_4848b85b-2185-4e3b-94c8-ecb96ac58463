/**
 * @Filename: task-item-schedule/cleaning/index.js
 * @Description:
 * @CreatedAt: 23/2/2021
 * @Author: HongKhanh
 **/
import React, { useMemo } from 'react';
import {
  BlockView,
  ConditionView,
  CText,
  DateTimeHelpers,
  FastImage,
  getTextWithLocale,
  TypeFormatDate,
} from '@btaskee/design-system';
import { get, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { iconPremiumHeader, icSchedule } from '@images';

import { StatusTask } from '../../../StatusTask';
import styles from './styles';

type TaskItemHeaderProps = {
  dataTask?: {
    task?: {
      createdAt?: string;
    };
    status?: string;
    timezone?: string;
  };
  index?: number;
};

const HeaderScheduleServiceName = ({ dataTask }: { dataTask: any }) => {
  const shouldRenderStar = useMemo(() => {
    if (dataTask?.isPremium === true) {
      return (
        <BlockView style={styles.blockIconImage}>
          <FastImage
            source={iconPremiumHeader}
            style={styles.iconImage}
          />
        </BlockView>
      );
    }
  }, [dataTask?.isPremium]);

  return (
    <BlockView
      flex
      row
    >
      <BlockView
        flex
        row
        center
      >
        <FastImage
          source={icSchedule}
          style={styles.icSchedule}
        />
        <BlockView flex>
          <CText
            bold
            testID={`serviceName${get(dataTask, 'description', null)}`}
            numberOfLines={2}
            style={styles.txt_serviceName}
          >
            {getTextWithLocale(get(dataTask, 'service.text', {}))}
          </CText>
        </BlockView>
      </BlockView>

      {shouldRenderStar}
    </BlockView>
  );
};

export const HeaderSchedule = ({ dataTask }: TaskItemHeaderProps) => {
  const { t } = useI18n();
  if (isEmpty(dataTask)) return null;

  const createdAt = dataTask?.task?.createdAt;
  return (
    <BlockView style={styles.headerContainer}>
      <BlockView
        flex
        style={[styles.headerServiceName]}
      >
        {/*  ======================= SERVICE NAME ======================= */}
        <HeaderScheduleServiceName dataTask={dataTask} />
        <ConditionView
          condition={!!createdAt}
          viewTrue={
            <CText style={styles.headerTxtPosted}>
              {t('TASK_SCHEDULE.CREATED_ON', {
                date: DateTimeHelpers.formatToString({
                  date: createdAt,
                  timezone: dataTask?.timezone,
                  typeFormat: TypeFormatDate.DateShort,
                }),
              })}
            </CText>
          }
        />
      </BlockView>
      <StatusTask dataTask={dataTask} />
    </BlockView>
  );
};
