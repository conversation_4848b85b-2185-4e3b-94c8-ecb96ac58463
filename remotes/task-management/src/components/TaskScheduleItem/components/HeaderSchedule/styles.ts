import { StyleSheet } from 'react-native';
import { ColorsV2, FontSizes, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  // Header styles
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.SPACE_16,
    paddingBottom: Spacing.SPACE_08,
    borderBottomWidth: 1,
    borderBottomColor: ColorsV2.neutral100,
  },
  headerServiceName: {
    width: '85%',
  },
  headerTxtPosted: {
    color: ColorsV2.neutral500,
    fontSize: FontSizes.SIZE_14,
    marginTop: Spacing.SPACE_04,
  },
  blockIconImage: {
    marginHorizontal: Spacing.SPACE_04,
  },
  iconImage: {
    width: Spacing.SPACE_20,
    height: Spacing.SPACE_20,
  },
  txt_serviceName: {
    color: ColorsV2.green500,
    fontSize: FontSizes.SIZE_16,
  },
  icSchedule: {
    width: Spacing.SPACE_20,
    height: Spacing.SPACE_20,
    marginRight: Spacing.SPACE_08,
  },
});
