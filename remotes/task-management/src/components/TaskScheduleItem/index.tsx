/**
 * @Filename: components/task-item/deep-cleaning/index.js
 * @Description:
 * @CreatedAt: 8/9/1010
 * @Author: <PERSON><PERSON>
 * @UpdatedAt: 4/1/2021
 * @UpdatedBy: HongKhanh, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>
 **/
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { BlockView } from '@btaskee/design-system';

import { AddressTaskItem } from '../AddressTaskItem';
import { DurationTaskItem } from '../DurationTaskItem';
import { TaskDetailOfService } from '../TaskDetailOfService';
import { WeekdaysTaskItem } from '../WeekdaysTaskItem';
import { HeaderSchedule } from './components/HeaderSchedule';
import { styles } from './styles';

interface TaskScheduleItemProps {
  index: number;
  dataTask: any;
  testID?: string;
  onPress: () => void;
}

export const TaskScheduleItem = ({
  index,
  dataTask,
  testID,
  onPress,
}: TaskScheduleItemProps) => {
  return (
    <BlockView style={styles.container}>
      <BlockView
        style={styles.wrapContainer}
        testID={testID}
      >
        <TouchableOpacity
          disabled={!onPress}
          testID={`task${dataTask.description}`}
          onPress={onPress}
        >
          <HeaderSchedule dataTask={dataTask} />
          <BlockView style={styles.wrapContent}>
            <DurationTaskItem
              dataTask={dataTask?.task}
              index={index}
            />
            <WeekdaysTaskItem
              dataTask={dataTask}
              index={index ?? 0}
            />
            <AddressTaskItem dataTask={dataTask} />
            <TaskDetailOfService dataTask={dataTask} />
          </BlockView>
        </TouchableOpacity>
      </BlockView>
    </BlockView>
  );
};
