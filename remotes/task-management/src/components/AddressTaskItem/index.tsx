/**
 * @Filename: components/task-item/components/address-task-item.js
 * @Description:
 * @CreatedAt: 22/02/2021
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @UpdatedAt: 22/02/2021
 * @UpdatedBy: HuuToan
 **/

import React from 'react';
import {
  BlockView,
  CText,
  IconImage,
  ITaskDetail,
} from '@btaskee/design-system';

import { icLocation } from '@images';

import { styles } from './styles';

export const AddressTaskItem = ({ dataTask }: { dataTask: ITaskDetail }) => {
  return (
    <BlockView style={styles.wrapItemDetailTask}>
      <IconImage
        source={icLocation}
        style={styles.imageIcon}
      />
      <BlockView style={styles.wrapText}>
        <CText
          numberOfLines={2}
          style={styles.txtItemDetailTask}
        >
          {dataTask?.address}
        </CText>
      </BlockView>
    </BlockView>
  );
};
