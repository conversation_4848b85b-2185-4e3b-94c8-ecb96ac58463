import { StyleSheet } from 'react-native';
import { BorderRadius, <PERSON>ceHelper, Spacing } from '@btaskee/design-system';

const { WIDTH } = DeviceHelper.WINDOW;
export default StyleSheet.create({
  // Header styles
  txt_statusTask: {
    paddingHorizontal: Spacing.SPACE_16,
    maxWidth: Math.round(WIDTH / 2.6),
  },
  statusContainer: {
    borderRadius: BorderRadius.RADIUS_08,
    padding: Spacing.SPACE_04,
  },
});
