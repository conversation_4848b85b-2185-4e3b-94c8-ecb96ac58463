/**
 * @Filename: task-item-schedule/cleaning/index.js
 * @Description:
 * @CreatedAt: 23/2/2021
 * @Author: HongKhanh
 **/
import React from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
} from '@btaskee/design-system';
import { getColorStatusOfTask, getStatusTask } from '@helper';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import styles from './styles';

type TaskItemHeaderProps = {
  dataTask?: {
    task?: {
      createdAt?: string;
    };
    status?: string;
    timezone?: string;
  };
  isPaymentShowFailed?: boolean;
  isTaskHaveLeader?: boolean;
};

export const StatusTask = ({
  dataTask,
  isPaymentShowFailed,
  isTaskHaveLeader,
}: TaskItemHeaderProps) => {
  const { t } = useI18n();
  const colorTaskStatus = getColorStatusOfTask(dataTask?.status);

  if (isEmpty(dataTask?.status)) return null;

  return (
    <BlockView>
      <ConditionView
        condition={!isEmpty(dataTask?.status)}
        viewTrue={
          <BlockView
            style={[
              styles.statusContainer,
              {
                backgroundColor: isPaymentShowFailed
                  ? ColorsV2.red200
                  : colorTaskStatus?.backgroundColor,
              },
            ]}
          >
            <CText
              center
              numberOfLines={2}
              style={[
                styles.txt_statusTask,
                {
                  color: isPaymentShowFailed
                    ? ColorsV2.red500
                    : colorTaskStatus?.color,
                },
              ]}
            >
              {t(
                isPaymentShowFailed
                  ? 'PAYMENT_FAILED'
                  : getStatusTask({
                      status: dataTask?.status,
                      isTaskHaveLeader,
                    }),
              )}
            </CText>
          </BlockView>
        }
      />
    </BlockView>
  );
};
