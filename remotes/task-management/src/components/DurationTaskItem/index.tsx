/**
 * @Filename: components/task-item/components/duration-task-item.js
 * @Description:
 * @CreatedAt: 22/02/2021
 * @Author: HuuToan
 * @UpdatedAt: 22/02/2021
 * @UpdatedBy: HuuToan
 **/

import React from 'react';
import {
  BlockView,
  CText,
  DateTimeHelpers,
  IconImage,
  ITaskDetail,
  PostTaskHelpers,
} from '@btaskee/design-system';
import { get } from 'lodash-es';

import { useI18n } from '@hooks';
import { icClock } from '@images';

import { styles } from './styles';

export const DurationTaskItem = ({
  dataTask,
  index,
}: {
  dataTask: ITaskDetail;
  index?: number;
}) => {
  const { t } = useI18n();
  const duration = get(dataTask, 'duration', null);
  if (!duration) {
    return null;
  }
  const timezone = DateTimeHelpers.getTimezoneByCity(dataTask?.taskPlace?.city);
  const durationFormat = PostTaskHelpers.formatHourDuration({
    duration,
    taskDate: dataTask.date,
    timezone,
  });
  let durationText = t('WORK_IN_TIME_FROM_A_TO_B', {
    t1: duration,
    t2: durationFormat.from,
    t3: durationFormat.to,
  });
  const testID = 'taskDuration';

  const gmt = DateTimeHelpers.getGMTByCompareTzDefault(timezone);
  if (gmt) {
    durationText = `${durationText} ${gmt}`;
  }

  if (dataTask?.dateOptions) {
    durationText = t('NUMBER_OF_HOURS', { t: duration });
  }

  return (
    <BlockView style={styles.wrapItemDetailTask}>
      <IconImage
        source={icClock}
        style={styles.imageIcon}
      />
      <BlockView style={{ justifyContent: 'center' }}>
        <CText
          testID={`${testID}${index}`}
          style={styles.txtItemDetailTask}
        >
          {durationText}
        </CText>
      </BlockView>
    </BlockView>
  );
};
