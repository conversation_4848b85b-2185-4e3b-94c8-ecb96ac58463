import React, { useCallback } from 'react';
import { ListRenderItemInfo } from 'react-native';
import { useAuth } from '@btaskee/auth-store';
import {
  BlockView,
  ColorsV2,
  CText,
  EndpointKeys,
  NavigationService,
  queryKeys,
  useAppStore,
  useFreshApiFocus,
} from '@btaskee/design-system';
import { useFocusEffect } from '@react-navigation/native';

import {
  EmptySchedule,
  ListTask,
  TaskScheduleItem,
  TaskSkeletonList,
} from '@components';
import { useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';

export function TabSchedule() {
  const { userId } = useAuth();
  const { isoCode } = useAppStore();
  const { t } = useI18n();

  const { data, refetch, isLoading } = useFreshApiFocus({
    key: EndpointKeys.getScheduleTasks,
    queryKey: queryKeys.tasks.schedule(userId!),
    params: {
      isoCode: isoCode!,
    },
    options: {
      enabled: !!userId && !!isoCode,
    },
  });

  useFocusEffect(
    useCallback(() => {
      if (userId && isoCode) {
        refetch();
      }
    }, [refetch, userId, isoCode]),
  );

  const renderItem = useCallback(({ item, index }: ListRenderItemInfo<any>) => {
    const newData = Object.assign({}, item);
    return (
      <TaskScheduleItem
        index={index}
        dataTask={newData}
        // onPress={() =>
        //   NavigationService.navigate('TaskManagement', {
        //     screen: RouteName.TaskDetail,
        //     params: {
        //       taskId: newData._id,
        //     },
        //   })
        // }
      />
    );
  }, []);

  return (
    <BlockView
      flex
      backgroundColor={ColorsV2.neutralBackground}
    >
      {isLoading ? (
        <TaskSkeletonList count={5} />
      ) : (
        <ListTask
          testID={'scrollSchedule'}
          data={data || []}
          renderItem={renderItem}
          refetch={refetch}
          ListEmptyComponent={<EmptySchedule />}
          contentNotLoggedIn={t('NO_USER_ACTIVITY_SCHEDULE_TITLE')}
        />
      )}
    </BlockView>
  );
}
