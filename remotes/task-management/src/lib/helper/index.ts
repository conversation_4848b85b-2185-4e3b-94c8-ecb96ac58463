import {
  DateTimeHelpers,
  i18n,
  IDate,
  IRequirement,
  SERVICES,
  STATUS_OF_PREPAYMENT,
  TASK_STATUS,
  TypeFormatDate,
} from '@btaskee/design-system';
import {
  LIMIT_HOURS_TO_CHANGE_TASK_AND_KEEP_TASKER,
  MIN_DURATION_OF_POST_TASK,
  TASK_STATUS_LIST,
} from '@constants';
import { isEmpty, sumBy } from 'lodash-es';

/**
 * @description get color of status task
 * @param taskStatus string
 * @returns Object
 * @example
 * taskStatus: CANCELED
 * result: { color: #FF3B30, backgroundColor: #FFEBEB }
 */
export const getColorStatusOfTask = (taskStatus = '') => {
  return TASK_STATUS_LIST.find((e) => e.key === taskStatus);
};

/**
 * @description get status task
 * @param status
 * @returns status
 */
export const getStatusTask = ({
  status = '',
  taskDate,
  isTaskHaveLeader,
}: {
  status?: string;
  taskDate?: IDate;
  isTaskHaveLeader?: boolean;
}) => {
  if (isEmpty(status)) {
    return '';
  }

  if (
    taskDate &&
    status === TASK_STATUS.CONFIRMED &&
    DateTimeHelpers.checkIsAfter({
      firstDate: taskDate,
      secondDate: new Date(),
      timezone: DateTimeHelpers.getTzDevice(),
    })
  ) {
    return 'LABEL_PROCESSING';
  }

  if (isTaskHaveLeader && status === TASK_STATUS.WAITING_ASKER_CONFIRMATION) {
    return 'LABEL_WAITING_ASKER_CONFIRMATION_TASK_LEADER';
  }

  return new Map([
    [TASK_STATUS.DONE, 'LABEL_DONE'],
    [TASK_STATUS.INACTIVE, 'TASK_SCHEDULE_INACTIVE'],
    [TASK_STATUS.ACTIVE, 'TASK_SCHEDULE_ACTIVE'],
    [TASK_STATUS.POSTED, 'LABEL_POSTED'],
    [TASK_STATUS.EXPIRED, 'LABEL_EXPIRED'],
    [TASK_STATUS.CONFIRMED, 'LABEL_CONFIRMED'],
    [
      TASK_STATUS.WAITING_ASKER_CONFIRMATION,
      'LABEL_WAITING_ASKER_CONFIRMATION',
    ],
    [TASK_STATUS.CANCELED, 'LABEL_CANCELED'],
    [TASK_STATUS.NEW, 'NOT_YET_PAYMENT'],
  ]).get(status) as string;
};

/**
 * @description return key status of payment from task
 * @param status
 * @returns NEW || PAID || ERROR || ''
 */
export const getStatusOfPayment = (status?: STATUS_OF_PREPAYMENT) => {
  let result = '';
  if (!status) {
    return result;
  }
  if (status === STATUS_OF_PREPAYMENT.new) {
    result = 'PAYMENT_STATUS_NEW';
  } else if (status === STATUS_OF_PREPAYMENT.error) {
    result = 'PAYMENT_STATUS_ERROR';
  } else if (status === STATUS_OF_PREPAYMENT.paid) {
    result = 'PAYMENT_STATUS_PAID';
  }
  return result;
};

/**
 * @description return icon status of payment from task
 * @param status
 * @returns NEW || PAID || ERROR || ''
 */
export const getIconOfPayment = (status?: STATUS_OF_PREPAYMENT) => {
  let result = null;
  if (!status) {
    return result;
  }
  if (status === STATUS_OF_PREPAYMENT.new) {
    result = 'icPaymentNew';
  } else if (status === STATUS_OF_PREPAYMENT.error) {
    result = 'icPaymentError';
  } else if (status === STATUS_OF_PREPAYMENT.paid) {
    result = 'icPaymentPaid';
  }
  return result;
};

/**
 * @param serviceName
 * @return String key localization
 * **/
export const getTextWaitingTaskerAccept = ({
  serviceName,
}: {
  serviceName?: SERVICES;
}) => {
  // For maid cleaner service should be
  let txtWaiting = 'WAITING_TASKER_ACCEPT';
  // For A/C, disinfection, upholstery service should be
  if (
    [
      SERVICES.AIR_CONDITIONER,
      SERVICES.DISINFECTION_SERVICE,
      SERVICES.SOFA,
    ].includes(serviceName as SERVICES)
  ) {
    txtWaiting = 'WAITING_ENGINEER_ACCEPT';
  }

  return txtWaiting;
};

/**
 * @description return key status of payment from task
 * @param status
 * @returns NEW || PAID || ERROR || ''
 */
export const getStatusOfRefund = (status?: STATUS_OF_PREPAYMENT) => {
  let result = '';
  if (!status) {
    return result;
  }
  if (status === STATUS_OF_PREPAYMENT.new) {
    result = 'TRUE_MONEY_REFUND_STATUS_NEW';
  } else if (status === STATUS_OF_PREPAYMENT.paid) {
    result = 'TRUE_MONEY_REFUND_STATUS_PAID';
  }
  return result;
};

export const isCanUpdateWithLimitHours = () => {
  const minHour = DateTimeHelpers.toDateTz({
    timezone: DateTimeHelpers.getTzDevice(),
  })
    .set('hour', LIMIT_HOURS_TO_CHANGE_TASK_AND_KEEP_TASKER.min)
    .startOf('hour');
  const maxHour = DateTimeHelpers.toDateTz({
    timezone: DateTimeHelpers.getTzDevice(),
  })
    .set('hour', LIMIT_HOURS_TO_CHANGE_TASK_AND_KEEP_TASKER.max)
    .startOf('hour');
  if (
    DateTimeHelpers.checkIsBetween({
      startDate: minHour,
      endDate: maxHour,
      timezone: DateTimeHelpers.getTzDevice(),
      unit: 'hour',
      inclusivity: '()',
    })
  ) {
    return true;
  }
  return false;
};

/**
 * @param {*} requirements of task
 * @returns Trả về duration nhở nhất mà người dùng có thể chọn khi update, post task, trong trường hợp task có requirements
 */
export const getMinDurationOfPostTask = (requirements: IRequirement[] = []) => {
  if (isEmpty(requirements)) {
    return MIN_DURATION_OF_POST_TASK;
  }
  return MIN_DURATION_OF_POST_TASK + sumBy(requirements, 'duration') || 0;
};
