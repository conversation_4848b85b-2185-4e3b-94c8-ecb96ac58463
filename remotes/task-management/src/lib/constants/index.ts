import { EndpointKeys, SERVICES, TASK_STATUS } from '@btaskee/design-system';

export const TASK_STATUS_LIST = [
  { key: TASK_STATUS.CANCELED, color: '#FF3B30', backgroundColor: '#FFEBEB' },
  { key: TASK_STATUS.CONFIRMED, color: '#20D327', backgroundColor: '#EBFFEF' },
  { key: TASK_STATUS.ACTIVE, color: '#20D327', backgroundColor: '#EBFFEF' },
  { key: TASK_STATUS.DONE, color: '#007AFF', backgroundColor: '#EBF4FF' },
  { key: TASK_STATUS.EXPIRED, color: '#888889', backgroundColor: '#F2F2F4' },
  { key: TASK_STATUS.POSTED, color: '#FF8228', backgroundColor: '#FBE6D7' },
  {
    key: TASK_STATUS.WAITING_ASKER_CONFIRMATION,
    color: '#FFCC00',
    backgroundColor: '#FDF8E2',
  },
  { key: TASK_STATUS.INACTIVE, color: '#888889', backgroundColor: '#F2F2F4' },
  { key: TASK_STATUS.NEW, color: 'red', backgroundColor: '#FFEBEB' },
];

export const LIMIT_DATE_HOME_MOVING = 15; // với dịch vụ home moving thì cho đặt trước 15 ngày

export const TimeSettingDefaultIC = {
  working: 9,
  minHourWorking: 24,
  limitDateBooking: 15,
};
export const MIN_HOUR_TO_UPDATE_TASK = 2;
export const HOUR_TO_SHOW_NOTE_UPDATE_TASK = 3;

// Thời gian tối thiểu mà khách hàng có thể update 1 công việc đã confirm
export const MIN_HOUR_TO_UPDATE_TASK_OF_SERVICES = {
  [SERVICES.INDUSTRIAL_CLEANING]: 2,
};

export const LIMIT_HOURS_TO_CHANGE_TASK_AND_KEEP_TASKER = {
  min: 5,
  max: 23,
};
export const MIN_DURATION_OF_POST_TASK = 2;

export const STATUS_TASK_CAN_UPDATE_NOTE = [
  TASK_STATUS.POSTED,
  TASK_STATUS.WAITING_ASKER_CONFIRMATION,
];
export const SERVICES_NOT_SUPPORT_KEEP_TASKER = [
  SERVICES.LAUNDRY,
  SERVICES.HOME_MOVING,
];

export const LIST_ENDPOINT_UPDATE_TASK = {
  [SERVICES.CLEANING]: EndpointKeys?.updateTaskHomeCleaning,
  [SERVICES.CLEANING_SUBSCRIPTION]: EndpointKeys?.updateTaskHomeCleaning,
  [SERVICES.HOUSE_KEEPING]: EndpointKeys?.updateHouseKeeping,
  [SERVICES.HOME_COOKING]: EndpointKeys?.updateTaskHomeCooking,
  [SERVICES.DEEP_CLEANING]: EndpointKeys?.updateTaskDeepCleaning,
  [SERVICES.AIR_CONDITIONER]: EndpointKeys?.updateTaskAirConditioner,
  [SERVICES.GROCERY_ASSISTANT]: EndpointKeys?.updateGroceryAssistant,
  [SERVICES.SOFA]: EndpointKeys?.updateTaskSofa,
  [SERVICES.ELDERLY_CARE]: EndpointKeys?.updateTaskElderlyCare,
  [SERVICES.ELDERLY_CARE_SUBSCRIPTION]: EndpointKeys?.updateTaskElderlyCare,
  [SERVICES.PATIENT_CARE]: EndpointKeys?.updateTaskPatientCare,
  [SERVICES.PATIENT_CARE_SUBSCRIPTION]: EndpointKeys?.updateTaskPatientCare,
  [SERVICES.DISINFECTION_SERVICE]: EndpointKeys?.updateTaskDisinfection,
  [SERVICES.CHILD_CARE]: EndpointKeys?.updateTaskChildCare,
  [SERVICES.CHILD_CARE_SUBSCRIPTION]: EndpointKeys?.updateTaskChildCare,
  [SERVICES.OFFICE_CLEANING]: EndpointKeys?.updateTaskOfficeCleaning,
  [SERVICES.OFFICE_CLEANING_SUBSCRIPTION]:
    EndpointKeys?.updateTaskOfficeCleaning,
  [SERVICES.WASHING_MACHINE]: EndpointKeys?.updateTaskWashingMachine,
  [SERVICES.WATER_HEATER]: EndpointKeys?.updateTaskWaterHeater,
  [SERVICES.OFFICE_CARPET_CLEANING]:
    EndpointKeys?.updateTaskOfficeCarpetCleaning,
  [SERVICES.MASSAGE]: EndpointKeys?.updateTaskMassage,
  [SERVICES.INDUSTRIAL_CLEANING]: EndpointKeys?.updateTaskIndustrialCleaning,
  [SERVICES.HOME_MOVING]: EndpointKeys?.updateTaskHomeMoving,
  [SERVICES.BEAUTY_CARE]: EndpointKeys?.updateTaskBeautyCare,
  [SERVICES.IRONING]: EndpointKeys?.updateTaskIroning,
  [SERVICES.HAIR_STYLING]: EndpointKeys?.updateTaskHairStyling,
  [SERVICES.MAKEUP]: EndpointKeys?.updateTaskMakeup,
  [SERVICES.NAIL]: EndpointKeys?.updateTaskNail,
};

export const LIST_ENDPOINT_GET_PRICE_UPDATE_TASK = {
  [SERVICES.CLEANING]: EndpointKeys?.getPriceCleaning,
  [SERVICES.CLEANING_SUBSCRIPTION]: EndpointKeys?.getPriceCleaningSubscription,
  // [SERVICES.HOUSE_KEEPING]: EndpointKeys?.getPriceHouseKeeping,
  // [SERVICES.HOME_COOKING]: EndpointKeys?.getPriceHomeCooking,
  // [SERVICES.DEEP_CLEANING]: EndpointKeys?.getPriceDeepCleaning,
  [SERVICES.AIR_CONDITIONER]: EndpointKeys?.getPriceAirConditioner,
  // [SERVICES.GROCERY_ASSISTANT]: EndpointKeys?.getPriceGroceryAssistant,
  // [SERVICES.SOFA]: EndpointKeys?.getPriceSofa,
  [SERVICES.ELDERLY_CARE]: EndpointKeys?.getPriceElderlyCare,
  [SERVICES.ELDERLY_CARE_SUBSCRIPTION]:
    EndpointKeys?.getPriceElderlyCareSubscription,
  [SERVICES.PATIENT_CARE]: EndpointKeys?.getPricePatientCare,
  [SERVICES.PATIENT_CARE_SUBSCRIPTION]:
    EndpointKeys?.getPricePatientCareSubscription,
  // [SERVICES.DISINFECTION_SERVICE]: EndpointKeys?.getPriceDisinfection,
  [SERVICES.CHILD_CARE]: EndpointKeys?.getPriceChildCare,
  [SERVICES.CHILD_CARE_SUBSCRIPTION]:
    EndpointKeys?.getPriceChildCareSubscription,
  [SERVICES.OFFICE_CLEANING]: EndpointKeys?.getPriceOfficeCleaning,
  [SERVICES.OFFICE_CLEANING_SUBSCRIPTION]:
    EndpointKeys?.getPriceOfficeCleaningSubscription,
  // [SERVICES.WASHING_MACHINE]: EndpointKeys?.getPriceWashingMachine,
  [SERVICES.WATER_HEATER]: EndpointKeys?.getPriceWaterHeater,
  // [SERVICES.OFFICE_CARPET_CLEANING]: EndpointKeys?.getPriceOfficeCarpetCleaning,
  // [SERVICES.MASSAGE]: EndpointKeys?.getPriceMassage,
  // [SERVICES.INDUSTRIAL_CLEANING]: EndpointKeys?.getPriceIndustrialCleaning,
  // [SERVICES.HOME_MOVING]: EndpointKeys?.getPriceHomeMoving,
  // [SERVICES.BEAUTY_CARE]: EndpointKeys?.getPriceBeautyCare,
  // [SERVICES.IRONING]: EndpointKeys?.getPriceIroning,
  // [SERVICES.HAIR_STYLING]: EndpointKeys?.getPriceHairStyling,
  // [SERVICES.MAKEUP]: EndpointKeys?.getPriceMakeup,
  // [SERVICES.NAIL]: EndpointKeys?.getPriceNail,
};

export const TYPE_WASHING_MACHINE = {
  topLoading: 'TOP_LOADING',
  frontLoading: 'FRONT_LOADING',
};
