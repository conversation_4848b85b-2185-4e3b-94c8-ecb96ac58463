import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorsV2.neutralWhite,
    padding: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
    marginVertical: Spacing.SPACE_08,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.SPACE_12,
  },
  title: {
    fontSize: FontSizes.SIZE_16,
    color: ColorsV2.neutral900,
  },
  content: {
    marginTop: Spacing.SPACE_08,
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.SPACE_08,
    borderBottomWidth: 1,
    borderBottomColor: ColorsV2.neutral100,
  },
  itemText: {
    fontSize: FontSizes.SIZE_14,
    color: ColorsV2.neutral700,
  },
  itemValue: {
    fontSize: FontSizes.SIZE_14,
    color: ColorsV2.neutral900,
  },
});
