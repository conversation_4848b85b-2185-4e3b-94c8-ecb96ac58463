import React from 'react';
import {
  BlockView,
  Colors,
  ConditionView,
  CText,
  FlatList,
  FlatListProps,
  formatMoney,
  getTextWithLocale,
  Icon,
  ISO_CODE,
  SizedBox,
  Spacing,
  TouchableOpacity,
  useAppStore,
} from '@btaskee/design-system';
import { ISelectedAC } from '@types';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import { getUnitACByIsoCode } from '../../lib/helper';
import { styles } from './styles';

const ARR_EMPTY = 0;
const UNIT = 1;

const RenderACDevice = ({
  optionAC,
  quantity,
  title,
  isLast,
}: {
  optionAC?: ISelectedAC['options'];
  quantity?: number;
  title?: string;
  isLast?: boolean;
}) => {
  const { t } = useI18n();

  if (!quantity) {
    return null;
  }

  return (
    <BlockView>
      <BlockView>
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>{t('AMOUNT')}</CText>
          <CText
            testID={`${title}Services`}
            style={styles.txtValue}
          >
            {quantity}
          </CText>
        </BlockView>
        <ConditionView
          condition={isEmpty(optionAC)}
          viewFalse={
            <BlockView style={styles.group}>
              <CText style={styles.txtVLabel}>{t('GAS_REFILL')}</CText>
              <CText
                testID={`${title}GasServices`}
                style={styles.txtValue}
              >
                {optionAC?.[0]?.quantity}
              </CText>
            </BlockView>
          }
        />
      </BlockView>
      <ConditionView
        condition={!isLast}
        viewTrue={
          <SizedBox
            height={1}
            color={Colors.BORDER_LIGHT_GRAY}
            margin={{
              top: Spacing.SPACE_04,
              bottom: Spacing.SPACE_12,
            }}
          />
        }
      />
    </BlockView>
  );
};

export const ListDeviceSelected = ({
  dataAC,
  setIndex,
  hideModal,
  scrollToBottom,
}: {
  hideModal: () => void;
  dataAC?: ISelectedAC[];
  setIndex: (index: number) => void;
  scrollToBottom: (value: boolean) => void;
}) => {
  const { t } = useI18n();
  const { isoCode } = useAppStore();

  const getTitleAC = (ac?: ISelectedAC) => {
    if (isoCode === ISO_CODE.TH) {
      const capacity = `${formatMoney(ac?.hp?.from || 0)} - ${formatMoney(
        ac?.hp?.to || 0,
      )} ${getUnitACByIsoCode(isoCode)}`;
      const title = getTextWithLocale(ac?.type?.text);

      return { title, capacity };
    }
    let capacity = '';
    const title = getTextWithLocale(ac?.type?.text);
    if ((ac?.hp?.from || 0) < (ac?.hp?.to || 0)) {
      capacity = t('CAPACITY_LESS_THAN', {
        t1: ac?.hp?.to,
        t2: getUnitACByIsoCode(isoCode),
      });
    }
    if ((ac?.hp?.from || 0) > (ac?.hp?.to || 0)) {
      capacity = t('CAPACITY_GREATER_THAN', {
        t1: ac?.hp?.from,
        t2: getUnitACByIsoCode(isoCode),
      });
    }
    if (ac?.hp?.from && ac?.hp?.to) {
      capacity = `${t('AIR_CONDITION_TITLE', {
        from: formatMoney(ac?.hp?.from || 0),
        to: formatMoney(ac?.hp?.to || 0),
        unit: getUnitACByIsoCode(isoCode),
      })}`;
    }
    return { title, capacity };
  };

  const handleOnPress = (data: ISelectedAC) => {
    hideModal();
    (data?.index || data?.index === ARR_EMPTY) && setIndex(data?.index || 0);
    scrollToBottom(Boolean(data?.hp?.from));
  };

  const _renderItem: FlatListProps<ISelectedAC>['renderItem'] = ({
    item,
    index,
  }) => {
    const isLast = index === (dataAC?.length || 0) - UNIT;
    const labelTypeAC = getTitleAC(item);
    const onPress = () => handleOnPress(item);

    return (
      <TouchableOpacity
        key={`${labelTypeAC.title}_${labelTypeAC.capacity}`}
        testID={`${labelTypeAC.title}_${labelTypeAC.capacity}`}
        activeOpacity={0.7}
        onPress={onPress}
      >
        <BlockView
          row
          horizontal
          padding={{ horizontal: Spacing.SPACE_16 }}
        >
          <BlockView flex>
            <BlockView
              row
              jBetween
              horizontal
            >
              <CText
                flex
                bold
                testID={labelTypeAC.title}
                style={styles.txtAC}
              >
                {labelTypeAC.title}
              </CText>
              <CText
                flex={2}
                right
                bold
                testID={labelTypeAC.capacity}
                color={Colors.GREY}
              >
                {labelTypeAC.capacity}
              </CText>
            </BlockView>
            <RenderACDevice
              optionAC={item?.options}
              quantity={item?.quantity}
              title={labelTypeAC.title}
              isLast={isLast}
            />
          </BlockView>
          <BlockView
            margin={{ left: Spacing.SPACE_08 }}
            padding={{ bottom: Spacing.SPACE_16 }}
          >
            <Icon
              name={'icChange'}
              color={Colors.ORANGE_4}
              size={18}
            />
          </BlockView>
        </BlockView>
      </TouchableOpacity>
    );
  };

  const _renderKey = (item: ISelectedAC) =>
    `${item?.type?.name}${item?.hp?.from}${item?.hp?.to}`;

  if (!dataAC || (dataAC && dataAC.length === ARR_EMPTY)) {
    return null;
  }

  return (
    <FlatList
      data={dataAC}
      renderItem={_renderItem}
      keyExtractor={_renderKey}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.containerScroll}
    />
  );
};
