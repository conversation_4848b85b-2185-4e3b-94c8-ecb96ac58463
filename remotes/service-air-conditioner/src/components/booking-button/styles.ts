import { StyleSheet } from 'react-native';
import { ColorsV2, FontSizes, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: ColorsV2.neutralWhite,
    paddingHorizontal: Spacing.SPACE_16,
    borderTopWidth: 1,
    borderTopColor: ColorsV2.neutral100,
  },
  pricePanel: {
    marginTop: Spacing.SPACE_12,
    paddingHorizontal: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPrice: {
    textAlign: 'right',
    color: ColorsV2.neutral900,
    fontSize: FontSizes.SIZE_14,
  },
  txtPromotion: {
    textDecorationColor: ColorsV2.orange500,
    textDecorationLine: 'line-through',
    color: ColorsV2.orange500,
    textAlign: 'right',
    fontSize: FontSizes.SIZE_14,
  },
  txtTotal: {
    color: ColorsV2.neutral900,
    fontSize: FontSizes.SIZE_16,
  },
});
