import React, { useMemo, useRef } from 'react';
import { Dimensions, ScrollView } from 'react-native';
import {
  BlockView,
  CModal,
  CText,
  Icon,
  Markdown,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

// Import components
import { styles } from './styles';

const { height } = Dimensions.get('window');

const _renderModalProcessStep = ({
  ref,
  processData,
}: {
  ref: any;
  processData: { title: string; content: string[] };
}) => {
  const { t } = useI18n();
  if (isEmpty(processData?.content)) {
    return null;
  }

  return (
    <CModal
      ref={ref}
      titleStyle={styles.titleModal}
      contentContainerStyle={styles.contentContainerStyle}
      title={processData.title}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{ marginBottom: Spacing.SPACE_12, minHeight: height / 2 }}
      >
        <CText
          bold
          style={styles.txtContent}
        >
          {t('AC_PROCESS_CONTENT')}
        </CText>
        {processData?.content.map((data, index) => {
          return (
            <BlockView
              row
              key={index}
              margin={{ top: Spacing.SPACE_04 }}
            >
              <BlockView style={styles.dotStyle} />
              <BlockView flex>
                <Markdown
                  text={data}
                  paragraphStyle={styles.txtContentStyle}
                />
              </BlockView>
            </BlockView>
          );
        })}
      </ScrollView>
    </CModal>
  );
};

export const ProcessAcStep = () => {
  const { t } = useI18n();
  const modalProcessStepRef = useRef<any>();

  const _handleOpen = () => {
    modalProcessStepRef?.current?.open();
  };

  const processData = useMemo(() => {
    return {
      title: t('AC_PROCESS_TITLE'),
      content: [
        t('AC_PROCESS_STEP_1'),
        t('AC_PROCESS_STEP_2'),
        t('AC_PROCESS_STEP_3'),
        t('AC_PROCESS_STEP_4'),
        t('AC_PROCESS_STEP_5'),
        t('AC_PROCESS_STEP_6'),
      ],
    };
  }, [t]);

  return (
    <BlockView>
      <TouchableOpacity
        onPress={_handleOpen}
        style={styles.processCleaningContainer}
      >
        <Icon
          name="icProcess"
          style={styles.iconProcessStyle}
        />
        <BlockView style={styles.processContentContainer}>
          <CText
            bold
            style={styles.processTxtContent}
          >
            {t('AC_PROCESS_ITEM')}
          </CText>
        </BlockView>
        <Icon
          name="icArrowRight"
          style={styles.iconArrowRightStyle}
        />
      </TouchableOpacity>
      {_renderModalProcessStep({
        ref: modalProcessStepRef,
        processData: processData,
      })}
    </BlockView>
  );
};
