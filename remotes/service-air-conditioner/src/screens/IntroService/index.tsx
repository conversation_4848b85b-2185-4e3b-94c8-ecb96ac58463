/**
 * @Filename: IntroService/index.tsx
 * @Description: Air Conditioner Service Introduction Screen
 * @CreatedAt: 16/9/2020
 * @Author: DucAnh
 * @UpdatedAt: 25/1/2021
 * @UpdatedBy: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
 **/

import React from 'react';
import { ScrollView } from 'react-native';
import {
  BlockView,
  CText,
  FastImage,
  Icon,
  IconProps,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useAppNavigation, useI18n } from '@hooks';
import { introHeader } from '@images';
import { RouteName } from '@navigation/RouteName';

import { styles } from './styles';

// Types
interface IntroItemProps {
  icon: IconProps['name'];
  title: string;
  value: string;
}

interface IntroServiceProps {
  // Add any props that might be passed to this component
  // Currently no props are being used
}

// Constants
const INTRO_ITEMS_CONFIG = [
  {
    icon: 'icIntroStar' as const,
    titleKey: 'AC_INTRO_PRO_TITLE',
    valueKey: 'AC_INTRO_PRO_CONTENT',
  },
  {
    icon: 'icIntroSafe' as const,
    titleKey: 'AC_INTRO_SAFE_TITLE',
    valueKey: 'AC_INTRO_SAFE_CONTENT',
  },
  {
    icon: 'icIntroPhone' as const,
    titleKey: 'AC_INTRO_READY_TITLE',
    valueKey: 'AC_INTRO_READY_CONTENT',
  },
  {
    icon: 'icIntroHeart' as const,
    titleKey: 'AC_INTRO_PRICE_TITLE',
    valueKey: 'AC_INTRO_PRICE_CONTENT',
  },
] as const;

// Components
const IntroItem: React.FC<IntroItemProps> = ({ icon, title, value }) => {
  const { t } = useI18n();

  return (
    <BlockView style={styles.wrapItemNote}>
      <Icon
        style={styles.imageIcon}
        resizeMode="cover"
        name={icon}
        size={Spacing.SPACE_40}
      />
      <BlockView style={styles.wrapTxtNote}>
        <CText
          bold
          style={styles.txt_note}
        >
          {title}
        </CText>
        <CText style={styles.txtValue}>{t(value)}</CText>
      </BlockView>
    </BlockView>
  );
};

export const IntroService: React.FC<IntroServiceProps> = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();

  const handleSubmit = async (): Promise<void> => {
    navigation.replace(RouteName.ChooseAddress);
  };

  const renderIntroItems = () => {
    return INTRO_ITEMS_CONFIG.map((item, index) => (
      <IntroItem
        key={index}
        icon={item.icon}
        title={t(item.titleKey)}
        value={item.valueKey}
      />
    ));
  };

  return (
    <BlockView
      inset="bottom"
      style={styles.container}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerStyle}
      >
        <BlockView style={styles.wrap_image}>
          <FastImage
            style={styles.imageStyle}
            source={introHeader}
          />
        </BlockView>

        <CText
          bold
          style={styles.txt_serviceName}
        >
          {t('AC_INTRO_TITLE')}
        </CText>

        <BlockView row>
          <Icon
            name="icStar"
            style={styles.iconStar}
            size={Spacing.SPACE_20}
          />
          <BlockView flex>
            <CText style={styles.txt_note}>{t('AC_INTRO_CONTENT')}</CText>
          </BlockView>
        </BlockView>

        <BlockView style={styles.wrapBottom}>{renderIntroItems()}</BlockView>
      </ScrollView>

      <TouchableOpacity
        style={styles.wrap_bottom}
        onPress={handleSubmit}
      >
        <CText
          bold
          style={styles.txtSubmit}
        >
          {t('INTRO_START_EXPERIENCE')}
        </CText>
      </TouchableOpacity>
    </BlockView>
  );
};
