import { Dimensions, StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const { width } = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralWhite,
  },
  containerTop: {},
  imageStyle: {
    height: width * 0.75,
    width: width,
  },
  wrap_image: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  txt_serviceName: {
    marginTop: Spacing.SPACE_24,
    fontSize: FontSizes.SIZE_16,
    textAlign: 'center',
    marginBottom: Spacing.SPACE_16,
    color: ColorsV2.neutral900,
  },
  txt_note: {
    color: ColorsV2.neutral900,
    lineHeight: Spacing.SPACE_24,
  },
  txt_QA: {
    color: ColorsV2.orange500,
  },
  txt_title: {
    color: ColorsV2.neutral700,
    fontSize: FontSizes.SIZE_14,
  },
  wrap_note: {
    marginTop: Spacing.SPACE_08,
  },
  wrapTxtNote: {
    flex: 1,
  },
  wrapItemNote: {
    flexDirection: 'row',
    marginTop: Spacing.SPACE_16,
  },
  wrapBottom: {
    marginTop: Spacing.SPACE_08,
  },
  titleBtn: {
    fontSize: FontSizes.SIZE_14,
    color: ColorsV2.neutralWhite,
  },
  wrap_bottom: {
    padding: Spacing.SPACE_16,
    margin: Spacing.SPACE_16,
    borderTopWidth: 1,
    borderTopColor: ColorsV2.neutral100,
    backgroundColor: ColorsV2.green500,
    borderRadius: BorderRadius.RADIUS_08,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wrap_QA: {
    backgroundColor: ColorsV2.neutral100,
    padding: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_08,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  wrap_QABottom: {
    backgroundColor: ColorsV2.neutral100,
    padding: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_08,
    marginTop: Spacing.SPACE_08,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  wrap_icon: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: Spacing.SPACE_04,
  },
  txtSubmit: {
    color: ColorsV2.neutralWhite,
  },
  imageIcon: {
    marginRight: Spacing.SPACE_16,
  },
  txtValue: {
    color: ColorsV2.neutral900,
    lineHeight: Spacing.SPACE_24,
    marginTop: Spacing.SPACE_04,
  },
  iconStar: {
    marginTop: Spacing.SPACE_04,
    marginRight: Spacing.SPACE_12,
  },
  contentContainerStyle: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingBottom: '20%',
  },
});
