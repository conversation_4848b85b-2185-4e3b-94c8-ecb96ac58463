/**
 * @Filename: air-conditioner/layout/item.js
 * @Description:
 * @CreatedAt: 14/10/2020
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @UpdatedAt: 6/1/2021
 * @UpdatedBy: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>
 **/

import React from 'react';
import {
  <PERSON>ert,
  AnimationHelpers,
  BlockView,
  Colors,
  CText,
  formatMoney,
  getTextWithLocale,
  Icon,
  ISO_CODE,
  Spacing,
  Switch,
  TouchableOpacity,
  useAppStore,
} from '@btaskee/design-system';
import { find, isEmpty } from 'lodash-es';

import { useChangeData, useI18n } from '@hooks';

import {
  CLEANING_AIR_CONDITIONER,
  MAX_QUANTITY_AIR_CONDITIONER,
} from '../../../../lib/constant';
import { getUnitACByIsoCode } from '../../../../lib/helper';
import {
  IDataACRender,
  IParamsRefillGas,
  ISelectedAC,
} from '../../../../types';
import { NoteGasRefill } from '../gas-refill';
import { styles } from './styles';

interface IAirConditionerItemProps {
  data?: IDataACRender;
  selectedAirConditioner?: ISelectedAC[];
  maximumPSI?: number;
  indexOfScene?: number;
}

const AirConditionerItemComponent = React.memo(
  ({
    data,
    selectedAirConditioner,
    maximumPSI,
    indexOfScene,
  }: IAirConditionerItemProps) => {
    const { t } = useI18n();
    const { isoCode } = useAppStore();
    const { addService, addOnServiceGas } = useChangeData();

    const handleClickQuantity =
      (quantity = 1) =>
      async () => {
        AnimationHelpers.runLayoutAnimation();
        const objAC = {
          hp: data?.hp,
          type: data?.type,
          index: indexOfScene,
        };
        await addService?.({ dataService: objAC, quantity });
      };

    const handleClickAddGas = (
      dataGas: IParamsRefillGas,
      quantity?: number,
    ) => {
      addOnServiceGas?.({ dataService: dataGas, quantity: quantity || 0 });
    };

    const _showAlert = (status?: boolean) => {
      if (status && maximumPSI) {
        Alert?.alert?.open({
          title: t('SV_AC_SCR2_POPUP_MAXIUM_GAS_TITLE'),
          message: {
            text: t('SV_AC_SCR2_POPUP_MAXIUM_GAS_REFILL'),
            params: { maxPSI: maximumPSI },
          },
          actions: [{ text: t('SV_AC_SCR2_POPUP_MAXIUM_GAS_CLOSE') }],
        });
      }
    };

    const selectedValue = selectedAirConditioner?.find(
      (ac) =>
        ac?.type?.name === data?.type?.name &&
        ac?.hp?.to === data?.hp?.to &&
        ac?.hp?.from === data?.hp?.from,
    );

    const shouldRenderGar = React.useMemo(() => {
      if (!data?.services || isEmpty(data?.services)) {
        return null;
      }
      const ACGasPump = data?.services.filter(
        (e) => e.name !== CLEANING_AIR_CONDITIONER,
      );
      return ACGasPump.map((gas, index) => {
        // Check for gas refill
        const isGasRefill = find(selectedValue?.options, { name: gas.name });
        // Get price by type
        const dataPrice = gas.prices?.find(
          (e) =>
            (e?.HPTo || 0) === (data?.hp?.to || 0) &&
            (e?.HPFrom || 0) === (data?.hp?.from || 0),
        );
        return (
          <BlockView key={index}>
            <BlockView
              row
              horizontal
              style={styles.gar}
            >
              <CText style={styles.txtGarPump}>
                {getTextWithLocale(gas?.text)?.toUpperCase()}
              </CText>
              <Switch
                testID={`switchAC_${selectedValue?.type?.name}_${gas.name}`}
                onValueChange={(status: boolean) => {
                  AnimationHelpers.runLayoutAnimation();
                  const quantity = status ? 1 : 0;
                  handleClickAddGas(
                    {
                      hp: data?.hp,
                      type: data?.type,
                      option: { name: gas?.name, text: gas?.text },
                    },
                    quantity,
                  );
                  _showAlert(status);
                }}
                value={isGasRefill}
              />
            </BlockView>
            {/* --------------------  Show note for gas refill -------------------- */}
            <NoteGasRefill
              isShow={Boolean(isGasRefill)}
              handleClickAddGas={handleClickAddGas}
              selectedValue={selectedValue}
              price={dataPrice?.price}
            />
          </BlockView>
        );
      });
    }, [data, selectedValue]);

    const shouldRenderContent = React.useMemo(() => {
      // not AC service selected
      if (!selectedValue) {
        return null;
      }

      // disabled button Plus
      const disabledButtonPlus = Boolean(
        (selectedValue?.quantity || 0) >= MAX_QUANTITY_AIR_CONDITIONER,
      );

      // render AC
      return (
        <BlockView style={styles.body}>
          <BlockView>
            <CText style={styles.txtSubTitle}>
              {t('SV_AC_SCR1_DETAIL_ITEM_AMOUNT').toUpperCase()}
            </CText>
          </BlockView>
          <BlockView
            row
            style={styles.wrap_quantity}
          >
            <TouchableOpacity
              testID="btnMinus"
              style={styles.btnAction}
              onPress={handleClickQuantity(-1)}
              disabled={!selectedValue.quantity}
            >
              <Icon
                name={'icSubtractFill'}
                color={Colors.BLACK_2}
                size={Spacing.SPACE_20}
              />
            </TouchableOpacity>
            <BlockView style={styles.wrap_txtQuantity}>
              <CText
                bold
                style={styles.txtQuantity}
              >
                {selectedValue.quantity}
              </CText>
            </BlockView>
            <TouchableOpacity
              testID={`btnPlus`}
              style={[
                styles.btnAction,
                disabledButtonPlus && styles.buttonInactive,
              ]}
              onPress={handleClickQuantity(1)}
              disabled={disabledButtonPlus}
            >
              <Icon
                name={'icPlusFill'}
                color={Colors.BLACK_2}
                size={Spacing.SPACE_20}
              />
            </TouchableOpacity>
          </BlockView>
          {shouldRenderGar}
        </BlockView>
      );
    }, [selectedValue, data]);

    const shouldRenderTitle = React.useMemo(() => {
      let title = getTextWithLocale(data?.type?.text);
      if ((data?.hp?.from || 0) < (data?.hp?.to || 0)) {
        title = `${t('SV_AC_SCR1_DETAIL_CAPACITY_LESS_THAN', {
          t1: data?.hp?.to,
          t2: getUnitACByIsoCode(isoCode),
        })}`;
      }
      if ((data?.hp?.from || 0) > (data?.hp?.to || 0)) {
        title = `${t('SV_AC_SCR1_DETAIL_CAPACITY_GREATER_THAN', {
          t1: data?.hp?.from,
          t2: getUnitACByIsoCode(isoCode),
        })}`;
      }
      if (data?.hp?.from && data?.hp?.to) {
        title = `${t('AIR_CONDITION_TITLE', {
          from: formatMoney(data?.hp?.from || 0),
          to: formatMoney(data?.hp?.to || 0),
          unit: getUnitACByIsoCode(isoCode),
        })}`;
      }
      if (isoCode === ISO_CODE.TH) {
        title = `${formatMoney(data?.hp?.from || 0)} - ${formatMoney(
          data?.hp?.to || 0,
        )} ${getUnitACByIsoCode(isoCode)}`;
      }
      return (
        <CText
          testID={`${data?.type?.name}_${title}`}
          bold
          style={[styles.txtTitle, selectedValue && styles.txtActive]}
        >
          {title}
        </CText>
      );
    }, [selectedValue]);

    const shouldRenderIconCheck = React.useMemo(() => {
      if (selectedValue) {
        return (
          <BlockView>
            <Icon
              name={'icTick'}
              size={Spacing.SPACE_20}
              color={Colors.SECONDARY_COLOR}
            />
          </BlockView>
        );
      }

      // Not choose, Show arrow down icon
      return (
        <BlockView center>
          <Icon
            name={'icArrowDown'}
            style={styles.imageArrowStyle}
            color={Colors.BLACK_2}
          />
        </BlockView>
      );
    }, [selectedValue]);

    return (
      <BlockView>
        <BlockView
          style={[styles.wrap_item, selectedValue && styles.borderActive]}
        >
          <TouchableOpacity
            onPress={handleClickQuantity(1)}
            disabled={Boolean(selectedValue)}
          >
            <BlockView
              row
              style={styles.header}
            >
              <BlockView
                vertical
                style={styles.leftHeader}
              >
                {shouldRenderTitle}
              </BlockView>
              {shouldRenderIconCheck}
            </BlockView>
          </TouchableOpacity>
          {shouldRenderContent}
        </BlockView>
      </BlockView>
    );
  },
);
export { AirConditionerItemComponent as AirConditionerItem };
