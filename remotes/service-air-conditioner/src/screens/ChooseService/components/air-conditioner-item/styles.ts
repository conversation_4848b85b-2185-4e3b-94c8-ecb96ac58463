import { StyleSheet } from 'react-native';
import {
  Colors,
  DeviceHelper,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const SIZE_BUTTON = Math.round(DeviceHelper.WINDOW.WIDTH / 10);

export const styles = StyleSheet.create({
  body: {
    padding: Spacing.SPACE_20,
    paddingTop: 0,
  },
  header: {
    // backgroundColor: 'red',
    padding: Spacing.SPACE_20,
  },
  buttonInactive: {
    backgroundColor: Colors.BORDER_LIGHT_GRAY,
  },
  // txtTitle: {
  //   color: constant.COLOR.black,
  //   textAlign: 'center',
  // },

  txtActive: {
    color: Colors.PRIMARY_COLOR,
  },
  borderActive: {
    borderColor: Colors.PRIMARY_COLOR,
  },
  txtGarPump: {
    flex: 1,
  },
  gar: {
    alignItems: 'center',
    marginTop: 20,
  },
  txtSubTitle: {
    marginTop: 5,
    marginBottom: 15,
  },
  btnAction: {
    height: SIZE_BUTTON,
    width: SIZE_BUTTON,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.WHITE,
    borderRadius: Spacing.SPACE_08,
  },
  wrap_quantity: {
    backgroundColor: Colors.GREY_2,
    padding: Spacing.SPACE_08,
    borderRadius: Spacing.SPACE_08,
  },
  txtQuantity: {
    textAlign: 'center',
    fontSize: FontSizes.SIZE_18,
  },
  wrap_txtQuantity: {
    flex: 1,
    justifyContent: 'center',
  },
  wrap_item: {
    backgroundColor: Colors.WHITE,
    // padding: constant.MARGIN.medium,
    borderRadius: Spacing.SPACE_08,
    // padding: constant.MARGIN.large,
    marginTop: Spacing.SPACE_08,
    borderWidth: 1,
    borderColor: Colors.BORDER_COLOR,
    overflow: 'hidden',
  },
  leftHeader: {
    flex: 1,
  },
  txtTitle: {
    color: Colors.BLACK,
  },
  imageArrowStyle: {
    width: FontSizes.SIZE_24,
    height: FontSizes.SIZE_24,
  },
});
