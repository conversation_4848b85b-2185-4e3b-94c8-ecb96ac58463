import React, { useEffect, useMemo, useRef } from 'react';
import { ScrollView } from 'react-native';
import {
  BlockView,
  ConditionView,
  CText,
  FastImage,
  HomeType,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';
import { useACStore } from '@store';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import { iconNoteAc } from '../../../../assets/images';
import { ProcessAcStep } from '../../../../components/process-ac-step';
import { ServiceGuaranteed } from '../../../../components/service-guaranteed';
import { SERVICE_AC_TYPES, TYPE_AC_SERVICE } from '../../../../lib/constant';
import { IDataACRender, ISelectedAC, ITypeOfCityAC } from '../../../../types';
import { AirConditionerItem } from '../air-conditioner-item';
import { ApartmentOption } from '../apartment-option';
import { styles } from './styles';

const NoteBuiltIn = () => {
  const { t } = useI18n();
  return (
    <BlockView
      row
      horizontal
      style={styles.boxNoteBuiltIn}
    >
      <FastImage
        source={iconNoteAc}
        style={styles.imgIconNote}
      />
      <BlockView flex>
        <CText
          bold
          style={styles.txtNoteBuiltIn}
        >
          {t('AC_NOTE_BUILD_IN')}
        </CText>
      </BlockView>
    </BlockView>
  );
};

interface SceneProps {
  route?: {
    key: number;
  } & ITypeOfCityAC;
  index: number;
  maximumPSI?: number;
  selectedAirConditioner?: ISelectedAC[];
  serviceByCity?: any;
  isScrollToBottom?: boolean;
}

const SceneComponent = ({
  route,
  index,
  selectedAirConditioner,
  serviceByCity,
  maximumPSI,
  isScrollToBottom = false,
}: SceneProps) => {
  const scrollRefScene = useRef<ScrollView>(null);
  const { service } = useACStore();
  const addonsApartmentByService = service?.addons?.find(
    (e: { name: string }) => e?.name === HomeType.Apartment,
  );

  useEffect(() => {
    setTimeout(() => {
      if (isScrollToBottom) {
        scrollRefScene?.current?.scrollToEnd({
          // animated: !ConfigHelpers.isE2ETesting,
        });
      } else {
        scrollRefScene?.current?.scrollTo({
          x: 0,
          y: 0,
          // animated: !ConfigHelpers.isE2ETesting,
        });
      }
    }, 300);
  }, [index, isScrollToBottom]);
  /**
   * Refactor data AC
   *
   * return Array
   */
  const dataAc = useMemo(() => {
    const dataAC: IDataACRender[] = [];
    const subService = route?.services?.[0]?.prices || [];
    subService.map((ac) => {
      dataAC.push({
        type: {
          name: route?.name,
          text: route?.text,
        },
        hp: { from: ac?.HPFrom || null, to: ac.HPTo || null },
        services: route?.services,
      });
    });
    return dataAC;
  }, [route, index]);

  const getImageBackground = useMemo(() => {
    const typeACService = TYPE_AC_SERVICE.find(
      (e) => e.type === serviceByCity?.type[index]?.name,
    );
    return typeACService?.imageBackground;
  }, [serviceByCity, index]);

  if (isEmpty(serviceByCity?.type)) {
    return null;
  }

  return (
    <ScrollView
      ref={scrollRefScene}
      testID={`${route?.name}_scrollPostTaskStep2AC`}
      contentContainerStyle={styles.containerScroll}
      showsVerticalScrollIndicator={false}
    >
      <BlockView>
        {getImageBackground ? (
          <FastImage
            resizeMode={'cover'}
            style={styles.headerImage}
            source={getImageBackground}
          />
        ) : null}
        <BlockView style={styles.wrapTypeAC}>
          {dataAc?.map((acService, i) => {
            return (
              <AirConditionerItem
                indexOfScene={index}
                maximumPSI={maximumPSI}
                key={`itemAcService_${i}`}
                data={acService}
                selectedAirConditioner={selectedAirConditioner}
              />
            );
          })}
        </BlockView>
        {route?.name === SERVICE_AC_TYPES.BUILT_IN ? <NoteBuiltIn /> : null}
      </BlockView>
      <BlockView style={{ marginHorizontal: Spacing.SPACE_16 }}>
        <ConditionView
          condition={Boolean(addonsApartmentByService)}
          viewTrue={<ApartmentOption />}
        />
        <ProcessAcStep />
        <ServiceGuaranteed />
      </BlockView>
      <SizedBox style={styles.styleBottom} />
    </ScrollView>
  );
};
export { SceneComponent as Scene };
