/**
 * @Filename: components/optional/index.js
 * @Description:
 * @CreatedAt: 6/5/2020
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @UpdatedAt: 05/11/2021
 * @UpdatedBy: <PERSON><PERSON>, HongKhan<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
 **/

import React, { useState } from 'react';
import {
  Alert,
  BlockView,
  CText,
  formatMoney,
  HomeType,
  Icon,
  Markdown,
  Switch,
  TouchableOpacity,
  useSettingsStore,
} from '@btaskee/design-system';
import { useACStore } from '@store';
import { cloneDeep, isEmpty } from 'lodash-es';

import { useI18n, usePostTask } from '@hooks';

import { IAddons } from '../../../../types';
import { styles } from './styles';

export const ApartmentOptional = () => {
  const { currency } = useSettingsStore()?.settings;
  const { service } = useACStore();

  const addonsApartmentByService = service?.addons?.find(
    (e: IAddons) => e?.name === HomeType.Apartment,
  );

  const { t } = useI18n();

  return (
    <BlockView style={styles.content}>
      <Markdown
        textStyle={styles.textStyle}
        text={t('AIR_CONDITIONER.APARTMENT_CONTENT_1', {
          currency: currency,
          fee: formatMoney(addonsApartmentByService?.cost),
        })}
      />
      <Markdown
        textStyle={styles.textStyle}
        text={t('AIR_CONDITIONER.APARTMENT_CONTENT_2')}
      />
      <BlockView style={styles.boxNote}>
        <CText>{t('AIR_CONDITIONER.APARTMENT_NOTE')}</CText>
      </BlockView>
    </BlockView>
  );
};

export const ApartmentOption = ({}) => {
  const { homeType, service, addons, setAddons } = useACStore();
  const { getPrice } = usePostTask();

  const changeApartmentOption = async (isChooseApartment: boolean) => {
    const addonsService = service?.addons;
    const addonsPostTask = cloneDeep(addons) || [];
    const addonsApartment = addonsPostTask?.find(
      (e) => e?.name === HomeType.Apartment,
    );

    // add addons for apartment
    if (isChooseApartment && !addonsApartment) {
      const addonsApartmentService = addonsService?.find(
        (e) => e?.name === HomeType.Apartment,
      );
      !isEmpty(addonsApartmentService) &&
        addonsPostTask.push(addonsApartmentService);
    }

    // remove addons for apartment
    if (!isChooseApartment && addonsApartment) {
      addonsPostTask.splice(addonsPostTask.indexOf(addonsApartment), 1);
    }

    // set addons and get price
    await setAddons(addonsPostTask);
    return await getPrice?.(service?.name);
  };

  const [isApartment, setIsApartment] = useState(
    Boolean(homeType === HomeType.Apartment),
  );
  const { t } = useI18n();

  const openModalDetail = () => {
    Alert?.alert?.open({
      title: t('AIR_CONDITIONER.APARTMENT_TITLE'),
      message: <ApartmentOptional />,
      actions: [
        {
          text: t('UNDERSTOOD'),
        },
      ],
    });
  };
  const _onChangeSwitch = (checked: boolean) => {
    setIsApartment(checked);
    changeApartmentOption(checked);

    // Choose premium, open modal intro
    if (checked) {
      openModalDetail();
    }
  };

  return (
    <>
      <BlockView style={styles.container}>
        <BlockView
          row
          style={styles.group}
        >
          <BlockView
            row
            style={styles.left}
          >
            <BlockView>
              <Icon
                name={'icApartment'}
                style={styles.iconImage}
              />
            </BlockView>
            <CText
              testID="txtChooseApartmentOptional"
              style={styles.txtLabel}
            >
              {t('APARTMENT')}
            </CText>
            <TouchableOpacity
              testID="chooseApartmentDescription"
              onPress={openModalDetail}
              style={styles.btnInfo}
            >
              <Icon
                name={'icMoreInformation'}
                style={styles.iconMoreInformation}
              />
            </TouchableOpacity>
          </BlockView>
          <Switch
            testID="chooseApartment"
            value={isApartment}
            onValueChange={_onChangeSwitch}
          />
        </BlockView>
      </BlockView>
    </>
  );
};
