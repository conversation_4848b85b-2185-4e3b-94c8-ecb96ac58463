import React from 'react';
import {
  BlockView,
  Colors,
  CText,
  FastImage,
  formatMoney,
  Icon,
  Spacing,
  TouchableOpacity,
  useSettingsStore,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { imgNoteGasRefill } from '../../../../assets/images';
import { REFILL_GAS_AIR_CONDITIONER } from '../../../../lib/constant';
import { IParamsRefillGas, ISelectedAC } from '../../../../types';
import { styles } from './styles';

export const NoteGasRefill = ({
  isShow,
  handleClickAddGas,
  selectedValue,
  price,
}: {
  isShow: boolean;
  handleClickAddGas: (dataGas: IParamsRefillGas, quantity?: number) => void;
  selectedValue?: ISelectedAC;
  price?: number;
}) => {
  const { t } = useI18n();
  const { currency } = useSettingsStore()?.settings;

  const currentOption = selectedValue?.options?.find(
    (option) => option?.name === REFILL_GAS_AIR_CONDITIONER,
  );
  const isDisabled =
    (currentOption?.quantity || 0) >= (selectedValue?.quantity || 0);

  const _handleChangeQuantity = (quantity: number) => {
    handleClickAddGas(
      {
        hp: selectedValue?.hp,
        type: selectedValue?.type,
        option: { name: currentOption?.name, text: currentOption?.text },
      },
      quantity,
    );
  };

  if (!isShow || !selectedValue?.options) {
    return null;
  }

  return (
    <BlockView>
      <BlockView
        row
        horizontal
        margin={{ top: Spacing.SPACE_08 }}
      >
        <FastImage
          source={imgNoteGasRefill}
          style={styles.noteGasImage}
        />
        <BlockView flex>
          <CText style={styles.noteGasTitle}>
            {t('AIR_CONDITIONER_NOTE_GAS_REFILL')}
          </CText>
        </BlockView>
      </BlockView>
      <BlockView
        row
        horizontal
        style={styles.noteGasContainer}
        padding={{ top: Spacing.SPACE_16 }}
        border={{ top: { width: 1, color: Colors.PRIMARY_COLOR } }}
      >
        <BlockView
          row
          horizontal
        >
          <BlockView flex>
            <CText>{t('QUANTITY_GAS_REFILL')}</CText>
            <CText
              color={Colors.SECONDARY_COLOR}
              margin={{ top: Spacing.SPACE_04 }}
            >
              {t('PRICE_REFILL', {
                cost: formatMoney(price),
                currency: currency?.sign,
              })}
            </CText>
          </BlockView>
          <BlockView flex>
            <BlockView
              row
              style={styles.wrap_quantity}
            >
              <TouchableOpacity
                testID="btnMinusGas"
                style={styles.btnAction}
                onPress={() =>
                  _handleChangeQuantity((currentOption?.quantity || 0) - 1)
                }
              >
                <Icon
                  name={'icSubtractFill'}
                  color={Colors.BLACK_2}
                  size={Spacing.SPACE_20}
                />
              </TouchableOpacity>
              <BlockView style={styles.wrap_txtQuantity}>
                <CText
                  testID="txtQuantityOptions"
                  bold
                  style={styles.txtQuantity}
                >
                  {currentOption?.quantity || 1}
                </CText>
              </BlockView>
              <TouchableOpacity
                testID="btnPlusGas"
                style={isDisabled ? styles.btnActionDisable : styles.btnAction}
                onPress={() =>
                  _handleChangeQuantity((currentOption?.quantity || 0) + 1)
                }
                disabled={isDisabled}
              >
                <Icon
                  name={'icPlusFill'}
                  color={isDisabled ? Colors.BORDER_LIGHT_GRAY : Colors.BLACK_2}
                  size={Spacing.SPACE_20}
                />
              </TouchableOpacity>
            </BlockView>
          </BlockView>
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
