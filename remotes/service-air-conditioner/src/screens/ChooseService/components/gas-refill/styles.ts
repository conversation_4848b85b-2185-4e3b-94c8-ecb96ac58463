import { StyleSheet } from 'react-native';
import {
  Colors,
  DeviceHelper,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const SIZE_BUTTON = Math.round(DeviceHelper.WINDOW.WIDTH / 12);

export const styles = StyleSheet.create({
  wrap_quantity: {
    backgroundColor: Colors.BORDER_COLOR,
    padding: Spacing.SPACE_08,
    borderRadius: Spacing.SPACE_08,
  },
  btnAction: {
    height: SIZE_BUTTON,
    width: SIZE_BUTTON,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.WHITE,
    borderRadius: Spacing.SPACE_08,
  },
  wrap_txtQuantity: {
    flex: 1,
    justifyContent: 'center',
  },
  txtQuantity: {
    textAlign: 'center',
    fontSize: FontSizes.SIZE_18,
  },
  btnActionDisable: {
    height: SIZE_BUTTON,
    width: SIZE_BUTTON,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.UNDERLAY_COLOR,
    borderRadius: Spacing.SPACE_08,
  },
  noteGasContainer: {
    marginTop: Spacing.SPACE_16,
  },
  noteGasImage: {
    width: Math.round(DeviceHelper.WINDOW.WIDTH / 11),
    height: Math.round(DeviceHelper.WINDOW.WIDTH / 11),
  },
  noteGasTitle: {
    color: Colors.BLACK,
    marginLeft: Spacing.SPACE_16,
    fontSize: FontSizes.SIZE_12,
  },
});
