/**
 * @Filename: air-conditioner/layout.machines-list.js
 * @Description: Air conditioner machines list component with tabbed interface
 * @CreatedAt: 13/10/2020
 * @Author: Du<PERSON><PERSON><PERSON>
 * @UpdatedAt: 6/1/2020
 * @UpdatedBy: <PERSON><PERSON>, HongKhanh
 **/

import React, { useCallback, useMemo } from 'react';
import { Route, TabBar, TabView } from 'react-native-tab-view';
import {
  Colors,
  ConditionView,
  CText,
  IService,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { getTypeServiceAC } from '../../../../lib/helper';
import { ISelectedAC, ITypeOfCityAC } from '../../../../types';
import { Scene } from '../scene';
import { styles } from './styles';

interface IMachinesListProps {
  service?: IService & { maximumPSI?: number };
  address?: any;
  selectedAirConditioner?: ISelectedAC[];
  index: number;
  setIndex: (index: number) => void;
  isScrollToBottom?: boolean;
}

// Type for the route object compatible with TabView
interface ITabRoute extends Route {
  key: string;
  title?: string;
}

interface ITabBarItemProps {
  route: ITabRoute;
  focused: boolean;
  onPress: () => void;
}

// Type for city data
interface ICityData {
  name: string;
  type: ITypeOfCityAC[];
}

// Custom hook for service data processing
const useServiceData = (service: IService | undefined, address: any) => {
  return useMemo(() => {
    const serviceByCity = service?.detail?.city?.find(
      (cityItem: ICityData) => cityItem.name === address?.city,
    );

    const originalRoutes = getTypeServiceAC(serviceByCity?.type);
    // Convert numeric keys to string keys for TabView compatibility
    const routes = originalRoutes.map((route) => ({
      key: String(route.key),
      title: route.title,
    }));
    const isOnlyOneType = routes.length === 1;

    return {
      serviceByCity,
      originalRoutes,
      routes,
      isOnlyOneType,
    };
  }, [service, address]);
};

// Extracted TabBarItem component for better reusability
const TabBarItem: React.FC<ITabBarItemProps> = React.memo(
  ({ route, focused, onPress }) => (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.tabStyle, focused && styles.tabStyleActive]}
    >
      <CText style={[styles.label, focused && styles.activeLabel]}>
        {route.title || ''}
      </CText>
    </TouchableOpacity>
  ),
);

// Main component
export const MachinesList: React.FC<IMachinesListProps> = React.memo(
  ({
    service,
    address,
    selectedAirConditioner,
    index,
    setIndex,
    isScrollToBottom,
  }) => {
    const { serviceByCity, originalRoutes, routes, isOnlyOneType } =
      useServiceData(service, address);

    // Render scene callback - uses original route structure
    const renderScene = useCallback(
      ({ route }: { route: ITabRoute }) => {
        // Find the original route using the index
        const routeIndex = Number(route.key);
        const originalRoute = originalRoutes[routeIndex];

        return (
          <Scene
            route={originalRoute}
            index={index}
            selectedAirConditioner={selectedAirConditioner}
            serviceByCity={serviceByCity}
            maximumPSI={service?.maximumPSI}
            isScrollToBottom={isScrollToBottom}
          />
        );
      },
      [
        index,
        selectedAirConditioner,
        serviceByCity,
        service?.maximumPSI,
        isScrollToBottom,
        originalRoutes,
      ],
    );

    // Tab bar item renderer
    const renderTabBarItem = useCallback(
      ({ route, props }: { route: ITabRoute; props: any }) => {
        const focused =
          props.navigationState.index ===
          props.navigationState.routes.findIndex(
            (r: ITabRoute) => r.key === route.key,
          );

        return (
          <TabBarItem
            route={route}
            focused={focused}
            onPress={() => props.jumpTo(route.key)}
          />
        );
      },
      [],
    );

    // Custom tab bar renderer
    const renderTabBar = useCallback(
      (props: any) => (
        <ConditionView
          condition={!isOnlyOneType}
          viewTrue={
            <TabBar
              {...props}
              indicatorStyle={styles.backgroundWhite}
              activeColor={Colors.PRIMARY_COLOR}
              tabStyle={styles.tabStyleTabBar}
              style={styles.tabBarStyle}
              inactiveColor={Colors.BLACK}
              contentContainerStyle={styles.contentContainer}
              renderTabBarItem={({ route }) =>
                renderTabBarItem({ route, props })
              }
            />
          }
        />
      ),
      [isOnlyOneType, renderTabBarItem],
    );

    // Early return if no service data
    if (isEmpty(serviceByCity)) {
      return null;
    }

    return (
      <TabView
        swipeEnabled={false}
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        style={{
          marginTop: isOnlyOneType ? Spacing.SPACE_24 : Spacing.SPACE_0,
        }}
        renderTabBar={renderTabBar}
      />
    );
  },
);
