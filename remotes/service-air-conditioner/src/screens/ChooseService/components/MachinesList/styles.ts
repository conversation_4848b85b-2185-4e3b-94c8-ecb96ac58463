import { StyleSheet } from 'react-native';
import { Colors, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  backgroundWhite: {
    backgroundColor: Colors.WHITE,
  },
  tabStyleTabBar: {
    width: 'auto',
    paddingHorizontal: 8,
    marginVertical: Spacing.SPACE_08,
  },
  tabBarStyle: {
    backgroundColor: Colors.WHITE,
    // marginHorizontal: constant.MARGIN.small2,
  },
  contentContainer: {
    paddingLeft: Spacing.SPACE_08,
  },
  labelStyle: {
    textAlign: 'center',
  },
  tabStyle: {
    paddingHorizontal: Spacing.SPACE_32,
    paddingVertical: Spacing.SPACE_08,
    margin: Spacing.SPACE_08,
    backgroundColor: Colors.LIGHT_GREY_2,
    borderRadius: Spacing.SPACE_08,
  },
  tabStyleActive: {
    backgroundColor: Colors.PRIMARY_COLOR,
  },
  activeLabel: {
    color: Colors.WHITE,
    backgroundColor: Colors.PRIMARY_COLOR,
  },
  label: {
    textAlign: 'center',
    color: Colors.BLACK,
  },
});
