/**
 * @Filename: air-conditioner/layout/index.js
 * @Description: Choose Air Conditioner Service Screen
 * @CreatedAt: 14/10/2020
 * @Author: Duc<PERSON><PERSON>
 * @UpdatedAt: 8/12/2020
 * @UpdatedBy: DucA<PERSON>
 * @RefactoredAt: [Current Date]
 **/

import React, { useCallback, useEffect, useState } from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import {
  Al<PERSON>,
  AnimationHelpers,
  BlockView,
  checkSupportCity,
  CText,
  HomeType,
  IService,
  NotSupportCity,
  PostTaskHelpers,
  PriceButton,
  SERVICES,
} from '@btaskee/design-system';
import { useACStore } from '@store';
import { cloneDeep, isEmpty } from 'lodash-es';

import { useAppNavigation, useI18n, usePostTask } from '@hooks';
import { RouteName } from '@navigation/RouteName';

import { Recap } from '../../components/recap';
import { IDataACRender, IParamsRefillGas } from '../../types';
import { ApartmentOptional } from './components/apartment-option';
import { MachinesList } from './components/MachinesList';
import { styles } from './styles';

// Types
export interface IAirConditionerProps {
  addService?: (params: {
    dataService: IDataACRender;
    quantity: number;
  }) => void;
  resetState?: () => void;
  service?: IService;
  addOnServiceGas?: (dataGas: IParamsRefillGas, quantity?: number) => void;
  previousServiceId?: IService['_id'];
}

interface UIState {
  isRecapVisible: boolean;
  selectedMachineIndex: number;
  isScrollToBottom: boolean;
}

// Custom hook for apartment option management
const useApartmentOption = () => {
  const { service, addons, setAddons, address } = useACStore();
  const { getPrice } = usePostTask();

  const apartmentAddon = service?.addons?.find(
    (addon: any) => addon?.name === HomeType.Apartment,
  );

  const changeApartmentOption = useCallback(
    async (shouldChooseApartment: boolean) => {
      const serviceAddons = service?.addons;
      const currentAddons = cloneDeep(addons) || [];
      const existingApartmentAddon = currentAddons?.find(
        (addon) => addon?.name === HomeType.Apartment,
      );

      if (shouldChooseApartment && !existingApartmentAddon) {
        const apartmentServiceAddon = serviceAddons?.find(
          (addon) => addon?.name === HomeType.Apartment,
        );
        if (!isEmpty(apartmentServiceAddon)) {
          currentAddons.push(apartmentServiceAddon);
        }
      } else if (!shouldChooseApartment && existingApartmentAddon) {
        const addonIndex = currentAddons.indexOf(existingApartmentAddon);
        currentAddons.splice(addonIndex, 1);
      }

      await setAddons(currentAddons);
      return await getPrice();
    },
    [service, addons, setAddons, getPrice],
  );

  useEffect(() => {
    if (address.homeType === HomeType.Apartment && apartmentAddon) {
      changeApartmentOption(true);
    }
  }, [address.homeType, apartmentAddon, changeApartmentOption]);

  return {
    apartmentAddon,
    changeApartmentOption,
  };
};

// Custom hook for initialization
const useInitialization = (
  previousServiceId?: IService['_id'],
  resetState?: () => void,
) => {
  const { service, address, setDateTime } = useACStore();

  useEffect(() => {
    // Reset state if service changed
    if (previousServiceId && previousServiceId !== service?._id) {
      resetState?.();
    }

    // Set default date time
    setDateTime(
      PostTaskHelpers.getDefaultDateTime(
        {
          serviceName: SERVICES.AIR_CONDITIONER,
          defaultTaskTime: service?.defaultTaskTime,
        },
        service?.defaultTaskTime,
        address?.city,
      ),
    );
  }, [
    previousServiceId,
    service?._id,
    service?.defaultTaskTime,
    address?.city,
    resetState,
    setDateTime,
  ]);
};

// Main component
export const ChooseService: React.FC<IAirConditionerProps> = ({
  resetState,
  previousServiceId,
}) => {
  const { t } = useI18n();
  const navigation = useAppNavigation();

  // Store hooks
  const { service, address, price, addons, selectedAirConditioner } =
    useACStore();

  // Custom hooks
  const { apartmentAddon } = useApartmentOption();
  useInitialization(previousServiceId, resetState);

  // Local state
  const [uiState, setUIState] = useState<UIState>({
    isRecapVisible: false,
    selectedMachineIndex: 0,
    isScrollToBottom: false,
  });

  // Event handlers
  const toggleRecapVisibility = useCallback(() => {
    AnimationHelpers.runLayoutAnimation();
    setUIState((prev) => ({
      ...prev,
      isRecapVisible: !prev.isRecapVisible,
    }));
  }, []);

  const handleMachineTypeSelect = useCallback((machineIndex: number) => {
    setUIState((prev) => ({
      ...prev,
      selectedMachineIndex: machineIndex,
      isScrollToBottom: false,
    }));
  }, []);

  const navigateToDateTime = useCallback(() => {
    if (uiState.isRecapVisible) {
      toggleRecapVisibility();
    }
    navigation.navigate(RouteName.ChooseDateTime);
  }, [uiState.isRecapVisible, toggleRecapVisibility, navigation]);

  const checkApartmentRequirement = useCallback(() => {
    const hasApartmentAddon = addons?.find(
      (addon: any) => addon?.name === HomeType.Apartment,
    );

    // Show apartment selection if required but not selected
    if (!hasApartmentAddon && apartmentAddon) {
      Alert.alert.open({
        title: t('APARTMENT_TITLE'),
        message: <ApartmentOptional />,
        actions: [
          {
            text: t('BTN_BACK'),
            onPress: () => Alert.alert.close(),
            style: 'cancel',
          },
          {
            text: t('CONFIRM'),
            onPress: navigateToDateTime,
          },
        ],
      });
      return;
    }

    navigateToDateTime();
  }, [addons, apartmentAddon, t, navigateToDateTime]);

  const updateScrollState = useCallback((shouldScrollToBottom: boolean) => {
    setUIState((prev) => ({
      ...prev,
      isScrollToBottom: shouldScrollToBottom,
    }));
  }, []);

  // Check city support
  if (!checkSupportCity(service?.city, address?.city)) {
    return <NotSupportCity />;
  }

  return (
    <BlockView style={styles.container}>
      <BlockView
        flex
        style={styles.content}
      >
        <CText
          bold
          style={styles.txtChooseType}
        >
          {t('POST_TASK_AC_CHOOSE_TYPE')}
        </CText>

        <MachinesList
          address={address}
          service={service}
          selectedAirConditioner={selectedAirConditioner}
          index={uiState.selectedMachineIndex}
          setIndex={handleMachineTypeSelect}
          isScrollToBottom={uiState.isScrollToBottom}
        />

        <BlockView
          absolute={uiState.isRecapVisible}
          style={styles.wrapper}
          backgroundColor="rgba(0,0,0,0.65)"
          zIndex={4}
        >
          <TouchableWithoutFeedback onPress={toggleRecapVisibility}>
            <BlockView
              flex
              zIndex={2}
            />
          </TouchableWithoutFeedback>

          <BlockView zIndex={2}>
            <PriceButton
              pricePostTask={price}
              testID="btnNextStep2"
              onPress={checkApartmentRequirement}
              HeaderComponent={
                <Recap
                  onPress={toggleRecapVisibility}
                  isShow={uiState.isRecapVisible}
                  setIndex={(index: number) =>
                    setUIState((prev) => ({
                      ...prev,
                      selectedMachineIndex: index,
                    }))
                  }
                  hideModal={toggleRecapVisibility}
                  scrollToBottom={updateScrollState}
                />
              }
            />
          </BlockView>
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
