import React from 'react';
import {
  BlockView,
  CText,
  DateTimeHelpers,
  DateWithGMT,
  DurationWithGMT,
  TypeFormatDate,
} from '@btaskee/design-system';
import { useACStore } from '@store';

import { useI18n } from '@hooks';

import { styles } from './styles';

const WorkingTimeComponent = ({
  date,
  duration,
}: {
  date?: string;
  duration: number;
}) => {
  const { t } = useI18n();
  const { address } = useACStore();
  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
  return (
    <BlockView>
      <CText
        testID="timeToWork"
        bold
        style={styles.subPanel}
      >
        {t('TIME_TO_WORK')}
      </CText>

      <BlockView>
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>{t('WOKING_DAY')}</CText>
          <DateWithGMT
            testID={'workingDay'}
            timezone={timezone}
            date={date}
            typeFormat={TypeFormatDate.DateTimeFullWithDay}
            style={styles.txtValue}
          />
        </BlockView>
      </BlockView>

      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('WORK_IN')}</CText>
        <DurationWithGMT
          testID={'duration'}
          style={styles.txtValue}
          timezone={timezone}
          date={date}
          duration={duration}
        />
      </BlockView>
    </BlockView>
  );
};
export const WorkingTime = WorkingTimeComponent;
