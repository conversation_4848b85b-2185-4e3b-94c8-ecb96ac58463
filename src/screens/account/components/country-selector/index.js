/**
 * @Filename: account/components/country-selector/index.js
 * @Description: Country selector component for Account tab
 * @CreatedAt: 2025-07-07
 * @Author: AI Assistant
 */

import React, { useRef, useState } from 'react';
import { TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome5';

import { BlockView, CText, FastImage } from '@component';
import { COUNTRIES } from '@config';
import constant from '@constant';
import { LocalizationContext } from '@context';
import { getIsoCodeGlobal } from '@helper';

import ModalChooseCountry from './modal-choose-country';
import styles from './styles';

const CountrySelector = () => {
  const I18n = React.useContext(LocalizationContext);
  const modalRef = useRef();
  const currentIsoCode = getIsoCodeGlobal();
  const [selectedIsoCode, setSelectedIsoCode] = useState(currentIsoCode);

  // Find the selected country from COUNTRIES array
  const selectedCountry = COUNTRIES.find((country) => country.isoCode === selectedIsoCode);

  const openModal = () => {
    modalRef?.current?.open && modalRef?.current?.open();
  };

  const handleCountryChange = (country) => {
    setSelectedIsoCode(country.isoCode);
    // Here you can add logic to update the global state or make API calls
    // For now, we'll just update the local state
  };

  if (!selectedCountry) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <TouchableOpacity onPress={openModal} style={styles.selectorButton}>
        <BlockView style={styles.leftSection}>
          <FastImage style={styles.flagIcon} source={require('../../../lib/assets/images/flag.png')} />
          <CText style={styles.title}>Choose Country</CText>
        </BlockView>
        <BlockView style={styles.rightSection}>
          <FastImage style={styles.countryFlag} source={selectedCountry.flag} />
          <CText style={styles.countryName}>{I18n.t(selectedCountry.key)}</CText>
          <Icon
            name="chevron-right"
            size={15}
            color={constant.COLOR.grey}
            style={styles.chevronIcon}
          />
        </BlockView>
      </TouchableOpacity>
      <ModalChooseCountry
        ref={modalRef}
        selectedCountry={selectedCountry}
        onCountryChange={handleCountryChange}
      />
    </BlockView>
  );
};

export default CountrySelector;
