/**
 * @Filename: account/components/country-selector/styles.js
 * @Description: Styles for Country selector component
 * @CreatedAt: 2025-07-07
 * @Author: AI Assistant
 */

import { Dimensions, StyleSheet } from 'react-native';

import constant from '@constant';

const { width } = Dimensions.get('window');
const FLAG_WIDTH = width / 12;
const FLAG_HEIGHT = FLAG_WIDTH / 1.5;

export default StyleSheet.create({
  container: {
    marginTop: constant.MARGIN.medium,
    marginHorizontal: constant.MARGIN.medium,
    backgroundColor: constant.COLOR.white,
    borderRadius: constant.BORDER_RADIUS,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: constant.MARGIN.medium,
    paddingHorizontal: constant.PADDING_CONTENT,
    borderBottomWidth: 1,
    borderBottomColor: constant.COLOR.grey2,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flagIcon: {
    width: 24,
    height: 24,
    marginRight: constant.MARGIN.small,
  },
  title: {
    fontSize: constant.FONT_SIZE.medium,
    color: constant.COLOR.black,
    fontWeight: '500',
  },
  countryFlag: {
    width: FLAG_WIDTH,
    height: FLAG_HEIGHT,
    resizeMode: 'contain',
    marginRight: constant.MARGIN.small,
  },
  countryName: {
    fontSize: constant.FONT_SIZE.medium,
    color: constant.COLOR.grey,
    marginRight: constant.MARGIN.small,
  },
  chevronIcon: {
    marginLeft: constant.MARGIN.tiny,
  },
});
