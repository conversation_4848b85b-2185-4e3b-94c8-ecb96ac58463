/**
 * @Filename: account/components/country-selector/modal-choose-country.js
 * @Description: Modal for choosing country in Account tab
 * @CreatedAt: 2025-07-07
 * @Author: AI Assistant
 */

import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Dimensions, StyleSheet, TouchableHighlight, View } from 'react-native';
import { getBottomSpace } from 'react-native-iphone-x-helper';
import Icon from 'react-native-vector-icons/FontAwesome5';

import { CModal, CText, FastImage } from '@component';
import { COUNTRIES, ISO_CODE } from '@config';
import constant from '@constant';
import { LocalizationContext } from '@context';
import { getRemoteConfigSetting } from '@helper/remoteConfigKeychain';

const { width } = Dimensions.get('window');

const RATIO_SPACE_BOTTOM = 2;
const RATIO_SIZE = 18; // 1/18;
const RATIO_WIDTH = 1.5;

const HEIGHT_FLAG = Math.round(width / RATIO_SIZE);
const WIDTH_FLAG = Math.round(HEIGHT_FLAG * RATIO_WIDTH); // 1:1.5

const CountryItem = ({ country, selectedCountry, onPress }) => {
  const I18n = React.useContext(LocalizationContext);
  const isSelected = selectedCountry.isoCode === country.isoCode;

  return (
    <TouchableHighlight
      underlayColor={constant.UNDERLAY_COLOR}
      onPress={() => onPress(country)}
      testID={`btn${country.isoCode}`}
    >
      <View style={styles.countryItem}>
        <View style={styles.countryInfo}>
          <FastImage 
            style={[styles.flag, { marginRight: constant.MARGIN.medium }]} 
            source={country.flag} 
          />
          <CText style={styles.countryName}>{I18n.t(country.key)}</CText>
        </View>
        {isSelected && (
          <Icon 
            name="check" 
            size={20} 
            color={constant.SECONDARY_COLOR} 
          />
        )}
      </View>
    </TouchableHighlight>
  );
};

const CountryList = ({ onChooseCountry, selectedCountry, isShowMalaysia }) => {
  return (
    <View style={styles.container}>
      {COUNTRIES.map((country, index) => {
        // Hide Malaysia if not enabled
        if (country?.isoCode === ISO_CODE.MY && !isShowMalaysia) {
          return null;
        }
        
        return (
          <CountryItem
            key={index}
            country={country}
            selectedCountry={selectedCountry}
            onPress={onChooseCountry}
          />
        );
      })}
    </View>
  );
};

const ModalChooseCountry = forwardRef(({ selectedCountry, onCountryChange }, ref) => {
  const I18n = React.useContext(LocalizationContext);
  const modalRef = useRef();
  const [isShowMalaysia, setShowMalaysia] = useState(false);

  useEffect(() => {
    const initData = async () => {
      const { ENABLE_NEW_FEATURE_OPEN_MALAYSIA } = await getRemoteConfigSetting();
      setShowMalaysia(Boolean(ENABLE_NEW_FEATURE_OPEN_MALAYSIA));
    };
    initData();
  }, []);

  const handleClose = () => {
    modalRef?.current?.close && modalRef?.current?.close();
  };

  const handleOpen = () => {
    modalRef?.current?.open && modalRef?.current?.open();
  };

  const handleCountrySelect = (country) => {
    onCountryChange && onCountryChange(country);
    handleClose();
  };

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    open() {
      handleOpen();
    },
    close() {
      handleClose();
    },
  }));

  return (
    <CModal ref={modalRef} title={I18n.t('CHOOSE_COUNTRY_CODE')}>
      <CountryList
        isShowMalaysia={isShowMalaysia}
        onChooseCountry={handleCountrySelect}
        selectedCountry={selectedCountry}
      />
    </CModal>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: constant.COLOR.white,
    paddingVertical: constant.MARGIN.medium,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    paddingBottom: getBottomSpace() / RATIO_SPACE_BOTTOM,
  },
  countryItem: {
    padding: constant.PADDING_CONTENT,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  countryInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  flag: {
    width: WIDTH_FLAG,
    height: HEIGHT_FLAG,
    resizeMode: 'contain',
  },
  countryName: {
    fontSize: constant.FONT_SIZE.medium,
    color: constant.COLOR.black,
  },
});

export default ModalChooseCountry;
