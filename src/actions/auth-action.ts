import RNRestart from 'react-native-restart';
import AsyncStorage from '@react-native-async-storage/async-storage';
import _, { debounce } from 'lodash';

import { logOut } from '@api/authentication-api';
import { deleteAccount as deleteAccountAPI } from '@api/user-api';
import { AlertHolder } from '@component';
import { saveUserInfoToGlobal } from '@helper';
import { setDataNotify } from '@src/screens/notification/actions/index.js';
import { getDealOfReward } from '@src/screens/user-rewards/actions/index.js';

import { Alert } from '../lib/helper/alert.helpers';
import { getSetting, getSupportCity, showLoading } from './app-action.js';
import { CommunityActions } from './community-action';

export const AUTH_LOADING = 'AUTH/LOADING';
export const AUTH_SIGN_IN = 'AUTH/SIGN_IN';
export const AUTH_LOGOUT = 'AUTH/LOGOUT';

export const signIn = (userToken) => {
  return { type: AUTH_SIGN_IN, payload: userToken };
};

export const logout = () => async (dispatch, getState) => {
  // show loading
  await dispatch(showLoading());

  // remove user info from global varible
  const { auth } = getState();
  const userToken = _.get(auth, 'userToken', null);

  // Clear data user info in global variable
  await saveUserInfoToGlobal({ token: null, userId: null, isoCode: null });
  const fcmToken = await AsyncStorage.getItem('DEVICE_TOKEN');

  // Call api go logout
  await logOut({ token: userToken?.token, userId: userToken?.userId, raixPushToken: fcmToken });
  await dispatch({ type: AUTH_LOGOUT });
  await dispatch(CommunityActions.logoutCommunity());

  setTimeout(() => {
    // show loading
    dispatch(showLoading(false));
    RNRestart.Restart();
  }, 300);
};

export const logoutOnly = () => async (dispatch, getState) => {
  // remove user info from global variable
  const { auth, app } = getState();
  const userToken = _.get(auth, 'userToken', null);

  // Clear data user info in global variable
  await saveUserInfoToGlobal({ token: null, userId: null, isoCode: null });
  const fcmToken = await AsyncStorage.getItem('DEVICE_TOKEN');

  // Call api go logout
  await logOut({ token: userToken?.token, userId: userToken?.userId, raixPushToken: fcmToken });
  await dispatch({ type: AUTH_LOGOUT });
  await dispatch(CommunityActions.logoutCommunity());
};

export const setLoadingInitApp = (payload = true) => {
  return { type: AUTH_LOADING, payload };
};

export const deleteAccount = (reason: string) => async (dispatch) => {
  // alert
  const alertObj = {
    title: 'DIALOG_TITLE_INFORMATION',
    message: 'ERROR_TRY_AGAIN',
    actions: [{ text: 'CLOSE' }],
  };

  // show loading
  await dispatch(showLoading());

  const _onDeleteAccount = debounce(async () => {
    // Call api go delete account
    const result = await deleteAccountAPI(reason);

    // hide loading
    await dispatch(showLoading(false));

    if (result?.isSuccess) {
      alertObj.message = 'DELETE_ACCOUNT_SUCCESS';
      alertObj.onClose = () => dispatch(logout());
      alertObj.actions = [
        {
          text: 'CLOSE',
        },
      ];
    }

    const errorCode = result?.error?.code;
    switch (errorCode) {
      case 'USER_HAS_ACTIVE_TASK':
        alertObj.message = 'USER_HAS_ACTIVE_TASK';
        break;
      case 'USER_ALREADY_HAVE_TASK':
        alertObj.message = 'USER_ALREADY_HAVE_TASK';
        break;
      case 'USER_DELETED_TOO_MANY_TIMES':
        alertObj.message = 'USER_DELETED_TOO_MANY_TIMES';
        break;
      case 'DELETE_FAILED':
        alertObj.message = 'DELETE_ACCOUNT_FAILED';
        break;
      case 'USER_HAS_ACTIVE_SUBSCRIPTION':
        alertObj.message = 'USER_HAS_ACTIVE_SUBSCRIPTION';
        break;
      case 'USER_ALREADY_HAVE_SUBSCRIPTION':
        alertObj.message = 'USER_HAS_ACTIVE_SUBSCRIPTION';
        break;
      case 'USER_HAS_ACTIVE_SCHEDULE':
        alertObj.message = 'USER_HAS_ACTIVE_SCHEDULE';
        break;
      case 'USER_ALREADY_HAVE_SCHEDULE':
        alertObj.message = 'USER_HAS_ACTIVE_SCHEDULE';
        break;
      case 'USER_HAS_OUTSTANDING_PAYMENT_STATUS_NEW':
        alertObj.message = 'USER_HAS_OUTSTANDING_PAYMENT_STATUS_NEW';
        break;
      case 'USER_TYPE_IS_NOT_ASKER':
        alertObj.message = 'USER_TYPE_IS_NOT_ASKER';
        break;
      default:
        break;
    }

    return AlertHolder.alert.open(alertObj, true);
  }, 500);

  _onDeleteAccount();
};

// Tự động thông báo hết hạn JWT và logout sau 2s khi API bị lỗi 401 và text là token expired
export const unauthorize = () =>
  debounce((dispatch, getState) => {
    // Chỉ hiển thị khi user đã đăng nhập
    const { auth } = getState();
    const userToken = _.get(auth, 'userToken', null);
    if (!userToken) {
      return;
    }
    Alert.alert.open({
      title: (t) => t('DIALOG_TITLE_INFORMATION'),
      message: (t) => t('HOME_SCREEN.NOT_AUTHENTICATED'),
      actions: [{ testID: 'unauthorizeCloseBtn', text: (t) => t('CLOSE') }],
    });
    // Tự động logout
    dispatch(logoutOnly());
    // Tự động get lại setting
    dispatch(getSetting());
    // Tự động get lại support city
    dispatch(getSupportCity());
    // Tự động get lại list ưu đãi
    dispatch(getDealOfReward());
    // Lấy lại danh sách thông báo
    dispatch(setDataNotify([]));
  }, 2000);
