import _, { isEmpty } from 'lodash';

import { getConfigSpecialPreBookingAPI, getNewVersion, getSettingSystem, getSupportCityAPI } from '@api/other';
import { getOutstandingPaymentDebt } from '@api/outstanding-payment-api';
import { getEndYearReportApi } from '@api/report-api';
import { getTaskDone } from '@api/task-api';
import { getUser, updateLanguage, updateLastOnline, updateUserCountry } from '@api/user-api';
import { updateVoiceCallToken } from '@api/voice-call';
import { FROM_SCREENS, USER_STATUS } from '@config';
import {
  getIsoCodeGlobal,
  getVersionAppName,
  isOptionNever,
  saveSupportCityToGlobal,
  saveUserInfoToGlobal,
} from '@helper';
import { USER_NOT_FOUND } from '@helper/error-code-list';
import { ImageHelpers } from '@helper/image.helpers';
import moment from '@moment';
import { RouteName } from '@navigation/route-name';
import { updateUserCleverTap } from '@src/lib/tracking/track-clever-tap';
import { getPaymentCardList } from '@src/screens/card-list/actions';
import { getFinancialAccount } from '@src/screens/user-bpay/action';
import { getDealOfReward } from '@src/screens/user-rewards/actions';
import { TrackFirebaseAnalytics } from '@tracking/track-firebase-analytics';

import { unauthorize } from './auth-action';
import { CommunityActions } from './community-action';
import { resetFullState } from './post-task';

export const APP_LOADING = 'APP/LOADING';
export const APP_FIRST_OPEN = 'APP/FIRST_OPEN';
export const APP_SET_ISO_CODE = 'APP/SET_ISO_CODE';
export const APP_UPDATE_USER = 'APP/UPDATE_USER';
export const APP_GET_SETTING = 'APP/GET_SETTING';
export const APP_SET_LOADING_SETTING = 'APP/SET_LOADING_SETTING';
export const APP_SET_LOCALE = 'APP/SET_LOCALE';
export const APP_SET_REFERRAL_CODE = 'APP/REFERRAL_CODE';
export const APP_SET_SET_SEEN_INTRO_HOUSEKEEPING = 'APP/_SET_SET_SEEN_INTRO_HOUSEKEEPING';
export const APP_SET_PROFILE_TRACKING = 'APP_SET_PROFILE_TRACKING';
export const APP_CHECK_COMPARE_VERSION_APP = 'APP/CHECK_COMPARE_VERSION_APP';
export const APP_SET_GIFT_CODE_DATA = 'APP/SET_GIFT_CODE_DATA';
export const SERVICE_FIRST_OPEN_PATIENT_CARE = 'SERVICE/FIRST_OPEN_PATIENT_CARE';
export const SERVICE_FIRST_OPEN_ELDERLY_CARE = 'SERVICE/FIRST_OPEN_ELDERLY_CARE';
export const SERVICE_FIRST_OPEN_DISINFECTION = 'SERVICE/FIRST_OPEN_DISINFECTION';
export const SERVICE_FIRST_OPEN_AIR_CONDITIONER = 'SERVICE/FIRST_OPEN_AIR_CONDITIONER';
export const APP_SET_LOADING_REFRESH_HOME = 'APP/SET_LOADING_REFRESH_HOME';
export const SERVICE_FIRST_OPEN_CHILD_CARE = 'SERVICE/FIRST_OPEN_CHILD_CARE';
export const SERVICE_FIRST_OPEN_OFFICE_CLEANING = 'SERVICE/FIRST_OPEN_OFFICE_CLEANING';
export const SERVICE_FIRST_OPEN_WASHING_MACHINE = 'SERVICE/FIRST_OPEN_WASHING_MACHINE';
export const SERVICE_FIRST_OPEN_HOME_MOVING = 'SERVICE/FIRST_OPEN_HOME_MOVING';
export const SERVICE_FIRST_OPEN_WATER_HEATER = 'SERVICE/FIRST_OPEN_WATER_HEATER';
export const SERVICE_FIRST_OPEN_OFFICE_CARPET_CLEANING = 'SERVICE/FIRST_OPEN_OFFICE_CARPET_CLEANING';
export const SERVICE_FIRST_OPEN_MASSAGE = 'SERVICE/FIRST_OPEN_MASSAGE';
export const SERVICE_FIRST_OPEN_INDUSTRIAL_CLEANING = 'SERVICE/FIRST_OPEN_INDUSTRIAL_CLEANING';
export const SERVICE_FIRST_OPEN_CLEANING_SERVICE = 'SERVICE/FIRST_OPEN_CLEANING_SERVICE';

export const APP_SET_DATA_YEAR_END_SUMMARY = 'APP/SET_DATA_YEAR_END_SUMMARY';
export const APP_UPDATE_FAVORITE_SERVICE = 'APP/UPDATE_FAVORITE_SERVICE';
export const APP_OPEN_EXPLORE_BTASKEE = 'APP/OPEN_EXPLORE_BTASKEE';
export const APP_COUNT_OPEN_EXPLORE_BTASKEE = 'APP/COUNT_OPEN_EXPLORE_BTASKEE';

export const APP_GET_SUPPORT_CITY = 'APP/GET_SUPPORT_CITY';
export const APP_SET_NEXT_CHOOSE_COUNTRY = 'APP/SET_NEXT_CHOOSE_COUNTRY';

export const APP_SET_BAGE_TAB_ACCOUNT = 'APP/SET_BAGE_TAB_ACCOUNT';
export const APP_SET_BADGE_TAB_NOTIFICATION_MESSAGE = 'APP/APP_SET_BADGE_TAB_NOTIFICATION_MESSAGE';
export const APP_SET_SURVEY_DATA = 'APP/APP_SET_SURVEY_DATA';
export const SERVICE_FIRST_OPEN_COMMUNITY = 'APP/SERVICE_FIRST_OPEN_COMMUNITY';
export const APP_SET_LAST_TASK_CLEANING_SERVICE = 'APP/SET_LAST_TASK_CLEANING_SERVICE';
export const APP_SET_FIRST_OPEN_YEAR_END_2024 = 'APP/APP_SET_FIRST_OPEN_YEAR_END_2024';
export const APP_SET_CONFIG_SPECIAL_PRE_BOOKING = 'APP/APP_SET_CONFIG_SPECIAL_PRE_BOOKING';

export const setBageTabAccount = (payload = true) => {
  return { type: APP_SET_BAGE_TAB_ACCOUNT, payload };
};

const _setLoadingSetting = (payload = true) => {
  return { type: APP_SET_LOADING_SETTING, payload };
};

export const setSetting = (payload = true) => {
  return { type: APP_GET_SETTING, payload };
};

const _updateUserInfo = (payload) => {
  return { type: APP_UPDATE_USER, payload };
};

export const showLoading = (payload = true) => {
  return { type: APP_LOADING, payload };
};

export const setFirstOpen = (payload = false) => {
  return { type: APP_FIRST_OPEN, payload };
};

export const setFirstOpenElderlyCare = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_ELDERLY_CARE, payload };
};

export const setFirstOpenChildCare = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_CHILD_CARE, payload };
};

export const setFirstOpenOfficeCleaning = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_OFFICE_CLEANING, payload };
};

export const setFirstOpenPatientCare = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_PATIENT_CARE, payload };
};

export const setFirstOpenDisinfection = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_DISINFECTION, payload };
};

export const setFirstOpenAirConditioner = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_AIR_CONDITIONER, payload };
};

export const setFirstOpenWashingMachine = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_WASHING_MACHINE, payload };
};

export const setFirstOpenHomeMoving = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_HOME_MOVING, payload };
};

export const setFirstOpenWaterHeater = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_WATER_HEATER, payload };
};

export const setFirstOpenOfficeCarpetCleaning = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_OFFICE_CARPET_CLEANING, payload };
};

export const setFirstOpenMassage = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_MASSAGE, payload };
};

export const setFirstOpenCommunity = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_COMMUNITY, payload };
};

export const setIsoCode = (payload = 'VN') => {
  global.isoCode = payload;
  return { type: APP_SET_ISO_CODE, payload };
};

const _setLoadingRefreshHome = (payload = true) => {
  return { type: APP_SET_LOADING_REFRESH_HOME, payload };
};

export const setNextChooseCountry = (payload = true) => {
  return { type: APP_SET_NEXT_CHOOSE_COUNTRY, payload };
};

export const setFirstOpenYearEnd2024 = (payload = true) => {
  return { type: APP_SET_FIRST_OPEN_YEAR_END_2024, payload };
};

export const getSetting = (isoCode) => async (dispatch) => {
  // loading
  dispatch(_setLoadingSetting());
  // call get setting
  const setting = await getSettingSystem(isoCode);
  // hide loading
  dispatch(_setLoadingSetting(false));
  // success
  if (setting?.isSuccess) {
    dispatch(setSetting(setting.data));
    setting?.data?.isHaveEventConfig && dispatch(getConfigSpecialPreBooking());
    return;
  }
  // User not authenticated => show alert and logout
  // && setting?.error?.message === 'Unauthorized' sẽ bắt thêm khi nào làm chức năng refresh token
  if (setting?.error?.code === 401) {
    // Remove cache
    dispatch(resetFullState());
    dispatch(unauthorize());
  }
  return null;
  // return dispatch(setSetting({error: setting.error}));
};

export const setLocaleApp = (payload) => ({ type: APP_SET_LOCALE, payload });

export const getUserInfo = (callback) => async (dispatch, getState) => {
  // Check exist userId
  if (!global.userId) {
    return;
  }

  // Call api get user info
  const userInfo = await getUser();
  // Success
  if (userInfo?.isSuccess) {
    if (userInfo.data && userInfo.data?.status !== USER_STATUS.LOCKED) {
      // Set data user to reducer
      await dispatch(_updateUserInfo(userInfo.data));

      const { app, postTask } = getState();

      dispatch(
        _updateProfileTracking({
          dataProfileTracking: app?.dataProfileTracking,
          user: userInfo.data,
          location: postTask?.address,
        }),
      );

      callback && typeof callback === 'function' && callback(userInfo.data);
      return null;
    }
    // User locked => Logout here
    dispatch(resetFullState());
    dispatch(unauthorize());
  } else if (userInfo?.error?.code === USER_NOT_FOUND) {
    // Logout when user removed account
    dispatch(resetFullState());
    dispatch(unauthorize());
  }
  // Something wrong
  return 'error';
};

export const setReferralCode = (payload) => {
  return { type: APP_SET_REFERRAL_CODE, payload };
};
export const setSeenIntroHouseKeeping = (payload) => {
  return { type: APP_SET_SET_SEEN_INTRO_HOUSEKEEPING, payload };
};

export const setProfileTracking = (payload) => {
  return { type: APP_SET_PROFILE_TRACKING, payload };
};

export const checkCompareVersionApp = () => async (dispatch) => {
  const version = getVersionAppName();

  if (!version || !getIsoCodeGlobal()) {
    return;
  }

  const result = await getNewVersion();
  if (result?.isSuccess && result.data) {
    dispatch({ type: APP_CHECK_COMPARE_VERSION_APP, payload: result.data });
  }
};

export const setGiftCodeData = (data) => (dispatch) => {
  dispatch({ type: APP_SET_GIFT_CODE_DATA, payload: data });
};

export const setBadgeTabNotificationMessage = (data) => (dispatch) => {
  dispatch({ type: APP_SET_BADGE_TAB_NOTIFICATION_MESSAGE, payload: data });
};

export const setDataYearEndSummary = (data) => (dispatch) => {
  dispatch({ type: APP_SET_DATA_YEAR_END_SUMMARY, payload: data });
};

const _checkGiftCodeFromFriend =
  ({ giftCodeData, userId, navigation }) =>
  async (dispatch) => {
    setTimeout(() => {
      if (!userId) {
        return null;
      }

      if (giftCodeData?.giftId && giftCodeData?.userId) {
        // check userId, no set to reducer when this gift code is mine
        if (giftCodeData.userId === userId) {
          dispatch(setGiftCodeData(null)); // Clear gift code data
          return null;
        }

        return navigation.navigate(RouteName.MyRewards);
      }
    }, 300);
  };

/**
 * Returns an array of addresses from the given locations or the address from the postTaskLocation object.
 *
 * @param {Object} options - The options object.
 * @param {Array} options.locations - The array of location objects.
 * @param {Object} options.postTaskLocation - The postTaskLocation object.
 * @return {Array} An array of addresses.
 */
const getArrayAddress = ({ locations, postTaskLocation }) => {
  if (isEmpty(locations)) {
    return postTaskLocation?.address ? [postTaskLocation?.address] : [];
  }
  return locations?.map((item) => item?.address);
};

/**
 * Returns an array of cities from the given locations or the city from the postTaskLocation object.
 *
 * @param {Object} locations - The array of location objects.
 * @param {Object} postTaskLocation - The postTaskLocation object.
 * @return {Array} An array of city names.
 */
const getArrayCity = ({ locations, postTaskLocation }) => {
  if (isEmpty(locations)) {
    return postTaskLocation?.city ? [postTaskLocation?.city] : [];
  }
  return locations?.map((item) => item?.city);
};

const _updateProfileTracking =
  ({ dataProfileTracking, user, location }) =>
  async (dispatch) => {
    try {
      const dataUser = {};
      if (user?.email) {
        dataUser.email = user.email;
      } else {
        dataUser.email = _.get(user, 'emails.[0].address', null);
      }

      dataUser._id = _.get(user, '_id', null);
      dataUser.name = _.get(user, 'name', null);
      dataUser.phone = _.get(user, 'phone', null);

      // Tránh trường hợp địa chỉ mới null
      dataUser.address = getArrayAddress({ locations: user?.locations, postTaskLocation: location });
      dataUser.city = getArrayCity({ locations: user?.locations, postTaskLocation: location });

      dataUser.point = _.get(user, 'point', null);
      dataUser.rankName = _.get(user, 'rankInfo.rankName', null);
      dataUser.language = _.get(user, 'language', null);
      dataUser.countryCode = _.get(user, 'countryCode', null);
      dataUser.avatar = _.get(user, 'avatar', null);
      dataUser.createdAt = _.get(user, 'createdAt', null);
      dataUser.isoCode = _.get(user, 'isoCode', null);
      dataUser.taskDone = _.get(user, 'taskDone', null);
      dataUser.firstPostedTask = _.get(user, 'firstPostedTask', null);
      dataUser.lastPostedTask = _.get(user, 'lastPostedTask', null);
      dataUser.appVersion = _.get(user, 'appVersion', null);
      dataUser.referralCode = _.get(user, 'referralCode', null);
      dataUser.friendCode = _.get(user, 'friendCode', null);

      if (JSON.stringify(dataUser) !== JSON.stringify(dataProfileTracking)) {
        dispatch(setProfileTracking(dataUser));
        updateUserCleverTap(dataUser);
      }
    } catch (error) {}
  };

const getOutstandingPayment =
  ({ navigation, user }) =>
  async (dispatch) => {
    // Check exist userId
    if (!global.userId) {
      return;
    }

    const options = {
      userId: user?._id,
    };

    // Fetch the Outstanding payment debt
    const outstandingDebt = await getOutstandingPaymentDebt(options);

    if (outstandingDebt?.isSuccess && outstandingDebt?.data?.length > 0) {
      // If user has any outstanding payment, redirect user to out standing payment debt page
      return navigation.navigate(RouteName.OutstandingPayment, { outstanding: outstandingDebt.data });
    }

    // Do nothing
  };

const getListTaskDone =
  ({ navigation, settings, user }) =>
  async (dispatch) => {
    // Check exist userId
    if (!global.userId) {
      return;
    }

    const listTaskDone = await getTaskDone();
    // const listTaskConfirmedToDone = await getTaskConfirmedToDone(options);

    // if (listTaskConfirmedToDone?.data && listTaskConfirmedToDone.data.length > 0) {
    //   return this.props.navigation.navigate(RouteName.DoneTask, { taskId: listTaskConfirmedToDone.data[0]._id })
    // } else
    if (listTaskDone?.data && listTaskDone.data.length > 0 && !isOptionNever(settings?.settingRating)) {
      // Tracking event Rate Task in home in component Rating will be wrong
      const taskDone = _.get(user, 'taskDone', 0);
      TrackFirebaseAnalytics.rateTaskUser(taskDone);
      return navigation.navigate(RouteName.Rating, { taskId: listTaskDone.data[0]._id });
    }
  };

// This function will call in componentDidMount home screen
export const initDataApp =
  ({ navigation, locale, callbackSignIn }) =>
  async (dispatch, getState) => {
    const { app } = getState();

    // init data
    await dispatch(getSetting());
    dispatch(getSupportCity());
    dispatch(
      _checkGiftCodeFromFriend({ giftCodeData: app.giftCodeData, userId: app.user?._id, navigation: navigation }),
    );
    dispatch(getFinancialAccount());
    dispatch(getPaymentCardList());
    dispatch(getYearEndSummary());
    updateLastOnline();
    // call API here
    await dispatch(
      getUserInfo(async (userInfo) => {
        // callback function param pass from previous screen
        callbackSignIn?.(userInfo);

        // synchronized isoCode for user
        if (userInfo.isoCode && global.isoCode && userInfo.isoCode !== global.isoCode) {
          await updateUserCountry(global.isoCode);
        }

        // synchronized language for user
        // Only call when userId exist
        if (userInfo.language && locale && userInfo.language !== locale && global?.userId) {
          await updateLanguage(locale);
        }
        dispatch(setIsoCode(userInfo.isoCode));

        // synce voice call token Stringee
        if (!userInfo.voiceCallToken) {
          updateVoiceCallToken();
        }
      }),
    );

    setTimeout(() => {
      dispatch(getOutstandingPayment({ navigation: navigation, user: app.user }));
      // this._handleCleverTapEvent(nameEvent, dataEvent);
      dispatch(getListTaskDone({ navigation: navigation, settings: app.settings, user: app.user }));
    }, 1000);

    // Hide loading
    await dispatch(showLoading(false));
  };

export const updateDataAfterSignIn = (props) => async (dispatch, getState) => {
  // Show loading
  await dispatch(showLoading());

  const { token, userId, isoCode, callback, navigation, from, locale } = props;
  // save user info to global variable
  await saveUserInfoToGlobal({
    token: token,
    userId: userId,
    isoCode: isoCode,
  });

  const { app } = getState();

  // Check gift code share by my friend
  await dispatch(_checkGiftCodeFromFriend({ giftCodeData: app.giftCodeData, userId: userId, navigation: navigation }));

  // callback function param pass from previous screen
  // await dispatch(getUserInfo(callback));

  // if (navigation?.canGoBack() && routeName !== RouteName.Home) {
  //   await navigation?.popToTop();
  // }

  // if (routeName?.screen && routeName?.routeName) {
  //   navigation.navigate(routeName.routeName, {screen: routeName.screen});
  // } else {
  //   await navigation?.navigate(routeName);
  // }

  // Post task from sign in with social

  if (from === FROM_SCREENS.SIGN_IN_SOCIAL) {
    // Get init data app
    await dispatch(initDataApp({ navigation: navigation, callbackSignIn: callback, locale: locale }));
    return null;
  }

  if (from === FROM_SCREENS.SIGN_UP_PASSWORD || from === FROM_SCREENS.SIGN_UP_SOCIAL) {
    // Get init data app
    await dispatch(initDataApp({ navigation: navigation, callbackSignIn: callback, locale: locale }));
    return navigation.pop(2);
  }

  if (from === FROM_SCREENS.SIGN_IN_AND_SIGN_UP || from === FROM_SCREENS.FORGOT_PASSWORD) {
    // Get init data app
    await dispatch(initDataApp({ navigation: navigation, callbackSignIn: callback, locale: locale }));
    return navigation.pop(3);
  }

  // post task from sign in with password
  if (from === FROM_SCREENS.SIGN_IN_PASSWORD) {
    // Get init data app

    await dispatch(initDataApp({ navigation: navigation, callbackSignIn: callback, locale: locale }));
    return navigation.goBack();
  }

  // Get init data app
  await dispatch(initDataApp({ navigation: navigation, locale: locale }));

  const action = {
    index: 0,
    routes: [{ name: RouteName.Home }],
  };
  return navigation.reset(action);

  // if (routeName?.screen && routeName?.routeName) {
  //   navigation.navigate(routeName.routeName, {screen: routeName.screen});
  // } else if (routeName) {
  //   navigation?.navigate(routeName);
  // }
};

export const getSupportCity = () => async (dispatch) => {
  const result = await getSupportCityAPI();
  if (result?.isSuccess && result.data) {
    // save SupportCity to global, required
    saveSupportCityToGlobal(result.data);

    // save to store
    dispatch({ type: APP_GET_SUPPORT_CITY, payload: result.data });
  }
};

export const refreshHomeData =
  ({ navigation, locale }) =>
  async (dispatch) => {
    await dispatch(_setLoadingRefreshHome(true));
    dispatch(initDataApp({ navigation: navigation, locale: locale }));
    // dispatch(getMarketingCampaign());
    // dispatch(getSpecialMarketingCampaign());
    dispatch(getDealOfReward());
    setTimeout(() => {
      dispatch(_setLoadingRefreshHome(false));
    }, 1500);
  };

export const getYearEndSummary = () => async (dispatch) => {
  // Chạy đến hết ngày 31/01
  const currentTime = moment();
  const startDate = moment().startOf('day');
  const endDate = moment().year(2025).month(0).date(31).endOf('day');
  if (!currentTime.isBetween(startDate, endDate)) {
    return;
  }
  const result = await getEndYearReportApi();
  if (result?.isSuccess && result.data) {
    dispatch(setDataYearEndSummary(result.data));
  }
};

export const updateFavoriteServiceAction = (data) => (dispatch) => {
  dispatch({ type: APP_UPDATE_FAVORITE_SERVICE, payload: data });
};

export const setHadOpenExploreBtaskee = () => {
  return { type: APP_OPEN_EXPLORE_BTASKEE, payload: true };
};

export const setCountOpenExploreBtaskee = () => {
  return { type: APP_COUNT_OPEN_EXPLORE_BTASKEE };
};

export const setSurveyDataAction = (payload) => {
  return { type: APP_SET_SURVEY_DATA, payload };
};

export const setIsFirstOpenIndustrialCleaning = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_INDUSTRIAL_CLEANING, payload };
};

export const setIsFirstOpenCleaningService = (payload = false) => {
  return { type: SERVICE_FIRST_OPEN_CLEANING_SERVICE, payload };
};

export const setLastTaskCleaningService = (payload = false) => {
  return { type: APP_SET_LAST_TASK_CLEANING_SERVICE, payload };
};

export const setConfigSpecialPreBooking = (payload) => {
  return { type: APP_SET_CONFIG_SPECIAL_PRE_BOOKING, payload };
};

export const getConfigSpecialPreBooking = () => async (dispatch) => {
  const result = await getConfigSpecialPreBookingAPI();
  if (result?.isSuccess && result?.data) {
    dispatch(setConfigSpecialPreBooking(result.data));
    ImageHelpers.preLoadDataImage(result?.data?.appConfig?.cacheImages || []);
  }
};
