import { fetchAPI } from '@helper';
/**
 * @param fAccountId (required)
 */
export const getFinancialAccountAPI = async (options) => {
  return await fetchAPI('v3/api-asker-vn/get-financial-account', options);
};

/**
 * @param isoCode (required)
 * @param fAccountId (required)
 *
 * return example:
 * [{
 *  _id: '',
 *  type: 'C',
 *  amount: 10000
 *  accountType (M,...)
 *  userId: 'dsafsadfsdf'
 *  source: {name: 'PAY_SUBSCRIPTION', value: 'jshddfkhsdf'},
 * }]
 *
 */

export const getFinancialTransaction = async (options) => {
  return await fetchAPI('v3/api-asker-vn/get-financial-transaction', options);
};

/**
 *
 * return example
 * {
 *   duration: 3
 *   phone: "**********"
 *   orderId: "MVHTZ9NE"
 *   date: null
 *   type: "subscription"
 *   contactName: "Chị Hiền"
 *   _id: "XdvEE6nt258g2jXnd"
 *   serviceId: "pcZRQ6PqmjrAPe5gt"
 *   payment: {method: "CARD", cardId: "ibZad8NGYyy59kLdx", cardInfo:{type:'visa', number:'1998'}}
 *   address: "Hẻm 112 Phổ Quang, phường 9, Phú Nhuận, Hồ Chí Minh, Việt Nam"
 * }
 **/
export const getFinancialTransactionDetail = async (transactionId: string) => {
  return await fetchAPI('v3/api-asker-vn/get-detail-financial-transaction', { id: transactionId });
};

/**
 * @param isoCode (required)
 * @param userId (required)
 * @param transactionId (required)
 * @param reasonReport array [{vi,ko,en,th}]
 * @param otherReport
 */
export const reportTransaction = async (options) => {
  return await fetchAPI('v3/api-asker-vn/create-report-transaction', options);
};
