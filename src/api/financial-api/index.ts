import * as ID_API from './id';
import * as TH_API from './th';
import * as VN_API from './vn';

/**
 * @param fAccountId (required)
 */
export const getFinancialAccountAPI = async () => {
  // User not login
  if (!global.userId) {
    return null;
  }
  const options = {
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getFinancialAccountAPI,
    TH: TH_API.getFinancialAccountAPI,
    ID: ID_API.getFinancialAccountAPI,
  };
  return await combine[global.isoCode](options);
};

/**
 * @param isoCode (required)
 * @param fAccountId (required)
 *
 * return example:
 * [{
 *  _id: '',
 *  type: 'C',
 *  amount: 10000
 *  accountType (M,...)
 *  userId: 'dsafsadfsdf'
 *  source: {name: 'PAY_SUBSCRIPTION', value: 'jshddfkhsdf'},
 * }]
 *
 */

export const getFinancialTransaction = async (page: number, limit: number) => {
  const options = {
    page,
    limit,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getFinancialTransaction,
    TH: TH_API.getFinancialTransaction,
    ID: ID_API.getFinancialTransaction,
  };
  return await combine[global.isoCode](options);
};

/**
 *
 * return example
 * {
 *   duration: 3
 *   phone: "**********"
 *   orderId: "MVHTZ9NE"
 *   date: null
 *   type: "subscription"
 *   contactName: "Chị Hiền"
 *   _id: "XdvEE6nt258g2jXnd"
 *   serviceId: "pcZRQ6PqmjrAPe5gt"
 *   payment: {method: "CARD", cardId: "ibZad8NGYyy59kLdx", cardInfo:{type:'visa', number:'1998'}}
 *   address: "Hẻm 112 Phổ Quang, phường 9, Phú Nhuận, Hồ Chí Minh, Việt Nam"
 * }
 **/
export const getFinancialTransactionDetail = async (transactionId: string) => {
  // User not login
  if (!global.userId) {
    return null;
  }
  const combine = {
    VN: VN_API.getFinancialTransactionDetail,
    TH: TH_API.getFinancialTransactionDetail,
    ID: ID_API.getFinancialTransactionDetail,
  };
  return await combine[global.isoCode](transactionId);
};

/**
 * @param isoCode (required)
 * @param userId (required)
 * @param transactionId (required)
 * @param reasonReport array [{vi,ko,en,th}]
 * @param otherReport
 * @param paymentMethod (optional)
 */
export const reportTransaction = async ({
  transactionId: transactionId,
  reasonReport: reasonReport,
  otherReport: otherReport,
  paymentMethod: paymentMethod,
}) => {
  const options = {
    transactionId: transactionId,
    isoCode: global.isoCode,
  };
  if (reasonReport) {
    options.reasonReport = reasonReport;
  }
  if (otherReport) {
    options.otherReport = otherReport;
  }
  if (paymentMethod) {
    options.paymentMethod = paymentMethod;
  }
  const combine = {
    VN: VN_API.reportTransaction,
    TH: TH_API.reportTransaction,
    ID: ID_API.reportTransaction,
  };
  return await combine[global.isoCode](options);
};
