import { fetchAPI } from '@helper';

import {
  IAddMemberToBusiness,
  IAddMemberToLevel,
  ICreateBusinessLevel,
  ICreateBusinessParams,
  IGetTotalRevokeParams,
  IGetTotalTopUpParams,
  IListBusinessMemberTransactions,
  IListBusinessTransactions,
  IListMember,
  IParamsGetBusinessTopUpSettingInfo,
  IRemoveBusinessLevel,
  IRemoveMemberFormBusiness,
  IRemoveMemberFormGroup,
  IRevokeMemberLevelParams,
  ISendBusinessReport,
  ITopUpMemberLevelParams,
  IUpdateBusinessLevel,
  IUpdateGroupName,
  IVerifyMember,
} from './type';

// TODO: check lại endpoint Indo
export const getListMember = async (options: IListMember) => {
  return await fetchAPI('v3/api-asker-indo/list-members-by-level', options);
};

// TODO: check lại endpoint Indo
export const updateGroupName = async (options: IUpdateGroupName) => {
  return await fetchAPI('v3/api-asker-indo/update-member-level', options);
};

// TODO: check lại endpoint Indo

export const removeMemberFromBusiness = async (options: IRemoveMemberFormBusiness) => {
  return await fetchAPI('v3/api-asker-indo/remove-member-from-business', options);
};

// TODO: check lại endpoint Indo
export const removeMemberFromGroup = async (options: IRemoveMemberFormGroup) => {
  return await fetchAPI('v3/api-asker-indo/remove-members-level', options);
};

// TODO: check lại endpoint Indo
export const listBusinessMemberTransactions = async (options: IListBusinessMemberTransactions) => {
  return await fetchAPI('v3/api-asker-indo/list-business-member-transactions', options);
};

// TODO: check lại endpoint Indo
export const addMemberToLevel = async (options: IAddMemberToLevel) => {
  return await fetchAPI('v3/api-asker-indo/add-members-level', options);
};

// TODO: check lại endpoint Indo
export const createBusinessLevel = async (options: ICreateBusinessLevel) => {
  return await fetchAPI('v3/api-asker-indo/create-business-level', options);
};

// TODO: check lại endpoint Indo
export const updateBusinessLevel = async (options: IUpdateBusinessLevel) => {
  return await fetchAPI('v3/api-asker-indo/update-business-level', options);
};

// TODO: check lại endpoint Indo
export const removeBusinessLevel = async (options: IRemoveBusinessLevel) => {
  return await fetchAPI('v3/api-asker-indo/remove-business-level', options);
};

// TODO: check lại endpoint Indo
export const verifyMember = async (options: IVerifyMember) => {
  return await fetchAPI('v3/api-asker-indo/verify-members', options);
};

// TODO: check lại endpoint ID
export const AddMemberToBusiness = async (options: IAddMemberToBusiness) => {
  return await fetchAPI('v3/api-asker-indo/add-members-to-business', options);
};

// TODO: check lại endpoint ID
export const createBusiness = async (options: ICreateBusinessParams) => {
  return await fetchAPI('v3/api-asker-indo/create-business', options);
};

// TODO: check lại endpoint ID
export const getTotalTopUp = async (options: IGetTotalTopUpParams) => {
  return await fetchAPI('v3/api-asker-indo/get-total-topup-bpay', options);
};

// TODO: check lại endpoint ID
export const topUpMemberLevel = async (options: ITopUpMemberLevelParams) => {
  return await fetchAPI('v3/api-asker-indo/topup-members-level', options);
};

// TODO: check lại endpoint ID
export const getTotalRevoke = async (options: IGetTotalRevokeParams) => {
  return await fetchAPI('v3/api-asker-indo/get-total-revoke-bpay', options);
};

// TODO: check lại endpoint ID
export const revokeMemberLevel = async (options: IRevokeMemberLevelParams) => {
  return await fetchAPI('v3/api-asker-indo/revoke-members-level', options);
};
export const listBusinessTransactions = async (options: IListBusinessTransactions) => {
  return await fetchAPI('v3/api-asker-indo/list-business-transactions', options);
};
export const sendBusinessReport = async (options: ISendBusinessReport) => {
  return await fetchAPI('v3/api-asker-indo/send-business-report', options);
};

export const getBusinessTopUpSettingInfo = async (options: IParamsGetBusinessTopUpSettingInfo) => {
  return await fetchAPI('v3/api-asker-indo/get-business-topup-setting-info', options);
};
