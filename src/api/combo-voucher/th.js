import { fetchAPI } from '@helper';
/**
 * @param userId (optional)
 */
export const getComboVoucherAPI = async () => {
  return await fetchAPI('v3/api-asker-th/get-list-combo-voucher-th');
};
/**
 * @param userId (optional)
 * @param comboVoucherId (required)
 */
export const getComboVoucherDetailAPI = async (options) => {
  return await fetchAPI('v3/api-asker-th/get-combo-voucher-detail-th', options);
};
/**
 * @param options (required)
 * "userId": "0834567890",
    "payment": {
        "method": "CREDIT"
    },
    "comboVoucherId": "x63f5d657d4102a1527c86ed5"
 */
export const payComboVoucherAPI = async (options) => {
  return await fetchAPI('v2/payment/pay-combo-voucher-th', options);
};

/**
 * options: {userId: "xx", comboVoucherId: "xx"}
 * @param {*} options
 * @returns
 *
 */
export const getFreeComboVoucherAPI = async (options) => {
  return await fetchAPI('v3/api-asker-th/get-free-combo-voucher-th', options);
};
