import { fetchAPI } from '@helper';

import { IParamsAddHousekeepingLocation, IParamsDeleteHousekeepingLocation } from './type';

export const createHousekeepingLocation = async (data: IParamsAddHousekeepingLocation) => {
  return await fetchAPI('v3/api-asker-indo/add-housekeeping-location', data);
};

export const deleteRoomHousekeepingLocation = async (data: IParamsDeleteHousekeepingLocation) => {
  return await fetchAPI('v3/api-asker-indo/delete-housekeeping-location', data);
};

export const updateRoomHousekeepingLocation = async (data: IParamsAddHousekeepingLocation) => {
  return await fetchAPI('v3/api-asker-indo/update-housekeeping-location', data);
};
