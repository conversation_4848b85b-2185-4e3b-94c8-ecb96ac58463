import { ID, TH, VN } from '@config';
import { getIsoCodeGlobal } from '@helper';

import * as ID_API from './id';
import { IParamsAddHousekeepingLocation, IParamsDeleteHousekeepingLocation } from './type';
import * as VN_API from './vn';

export const createHousekeepingLocation = async (params: IParamsAddHousekeepingLocation) => {
  const combine = {
    [VN]: VN_API.createHousekeepingLocation,
    [TH]: () => null,
    [ID]: ID_API.createHousekeepingLocation,
  };
  return await combine[getIsoCodeGlobal()](params);
};

export const deleteRoomHousekeepingLocation = async (params: IParamsDeleteHousekeepingLocation) => {
  const combine = {
    [VN]: VN_API.deleteRoomHousekeepingLocation,
    [TH]: () => null,
    [ID]: ID_API.deleteRoomHousekeepingLocation,
  };
  return await combine[getIsoCodeGlobal()](params);
};

export const updateRoomHousekeepingLocation = async (
  params: IParamsAddHousekeepingLocation & { locationId?: string },
) => {
  const combine = {
    [VN]: VN_API.updateRoomHousekeepingLocation,
    [TH]: () => null,
    [ID]: ID_API.updateRoomHousekeepingLocation,
  };
  return await combine[getIsoCodeGlobal()](params);
};
