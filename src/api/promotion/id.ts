import { fetchAPI } from '@helper';

export const checkPromotion = async (options) => {
  return await fetchAPI('v3/promotion-indo/check-post-task', options, 'post', false);
};

export const getPromotionByService = async (options) => {
  return await fetchAPI('v3/api-asker-indo/get-gift-v2', options);
};

export const getRewardsByTask = async (options) => {
  return await fetchAPI('v3/api-asker-indo/get-rewards-for-book-task', options);
};
