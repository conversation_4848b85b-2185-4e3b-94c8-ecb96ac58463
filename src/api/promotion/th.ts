import { fetchAPI } from '@helper';
import { IRespond } from '@src/lib/types';

export const checkPromotion: (options: any) => Promise<IRespond> = async (options) => {
  return await fetchAPI('v3/promotion-th/post-task', options);
};

export const getPromotionByService = async (options) => {
  return await fetchAPI('v3/api-asker-th/get-gift-v2', options);
};

export const getRewardsByTask = async (options) => {
  return await fetchAPI('v3/api-asker-th/get-rewards-for-book-task', options);
};
