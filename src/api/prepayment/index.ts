import * as ID_API from './id';
import * as TH_API from './th';
import { ICancelTryPaymentProcessParams, ITrackTryToProcessPaymentParams, IUpdatePaymentMethodParams } from './types';
import * as VN_API from './vn';

/**
 * @description prepayment
 * **/
export const prepaymentAPI = async ({ taskId }: { taskId: string }) => {
  const options = {
    userId: global.userId,
    taskId,
  };
  const combine = {
    VN: VN_API.prepayment,
    TH: TH_API.prepayment,
    ID: ID_API.prepayment,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description update prepayment
 * **/
export const updatePaymentMethodAPI = async (options: IUpdatePaymentMethodParams) => {
  const combine = {
    VN: VN_API.updatePaymentMethod,
    TH: TH_API.updatePaymentMethod,
    ID: ID_API.updatePaymentMethod,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description update prepayment
 * **/
export const cancelPrepaymentAPI = async (options: IUpdatePaymentMethodParams) => {
  const combine = {
    VN: VN_API.cancelPrepayment,
    TH: TH_API.cancelPrepayment,
    ID: ID_API.cancelPrepayment,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description cancel try payment
 * **/
export const cancelTryPaymentProcessAPI = async (options: ICancelTryPaymentProcessParams) => {
  const combine = {
    VN: VN_API.cancelTryPaymentProcess,
    TH: TH_API.cancelTryPaymentProcess,
    ID: ID_API.cancelTryPaymentProcess,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description cancel try payment
 * **/
export const trackTryToProcessPaymentAPI = async (options: ITrackTryToProcessPaymentParams) => {
  const combine = {
    VN: VN_API.trackTryToProcessPayment,
    TH: TH_API.trackTryToProcessPayment,
    ID: ID_API.trackTryToProcessPayment,
  };
  return await combine[global.isoCode](options);
};
