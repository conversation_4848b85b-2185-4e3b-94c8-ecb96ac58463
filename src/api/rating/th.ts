import { fetchAPI } from '@helper';

/**
 * @param rate Number
 * @param taskId String
 * @param review String
 * @param feedBack Array
 * @param isFavourite Booleans
 * @param tip Number
 */
export const customerRating = async (options) => {
  return await fetchAPI('v3/rating-th/customer-rating', options);
};

export const updateRatingTask = async (options) => {
  return await fetchAPI('v3/rating-th/update', options);
};

/**
 * @param taskId String
 */
export const closeRatingTask = async (taskId) => {
  return await fetchAPI('v3/api-asker-th/close-rating-task', { taskId: taskId });
};

export const setReviewStore = async (options) => {
  return await fetchAPI('v3/api-asker-th/set-review-store', options);
};
