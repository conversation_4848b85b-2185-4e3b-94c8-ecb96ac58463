import { fetchAPI } from '@helper';

export const getPaymentCardListAPI = async (options) => {
  return await fetchAPI('v3/api-asker-th/get-list-payment', options);
};

export const removeCardPayment = async (options) => {
  return await fetchAPI('v2/payment/disable-card-th', options);
};

export const setPaymentCardDefault = async (options) => {
  return await fetchAPI('v3/api-asker-th/set-payment-card-default', options);
};
