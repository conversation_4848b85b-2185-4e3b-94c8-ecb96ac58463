import { fetchAPI } from '@helper';

export const getPaymentCardListAPI = async (options) => {
  return await fetchAPI('v3/api-asker-vn/get-list-payment', options);
};

export const removeCardPayment = async (options) => {
  return await fetchAPI('v2/payment/disable-card', options);
};

export const setPaymentCardDefault = async (options) => {
  return await fetchAPI('v3/api-asker-vn/set-payment-card-default', options);
};
