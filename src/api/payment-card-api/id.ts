import { ISO_CODE } from '@config';
import { fetchAPI, getIPAddress } from '@helper';

export interface ICardPaymentIDParams {
  userId: string;
  tokenId: string;
  cardNumber: string;
  holderName: string;
  expiryMonth: string;
  expiryYear: string;
  isoCode?: ISO_CODE;
  shopperIP?: any;
}

export const getPaymentCardListAPI = async (options) => {
  return await fetchAPI('v3/api-asker-indo/get-list-payment', options);
};

export const addCardPayment = async (options: ICardPaymentIDParams) => {
  options.shopperIP = await getIPAddress();
  options.isoCode = ISO_CODE.ID;
  return await fetchAPI('v3/payment-indo/integrate-card', options);
};

export const removeCardPayment = async (options) => {
  return await fetchAPI('v3/payment-indo/disable-card', options);
};

export const setPaymentCardDefault = async (options) => {
  return await fetchAPI('v3/api-asker-indo/set-payment-card-default', options);
};
