import { fetchAPI } from '@helper';

/**
  @param userId
  @param page
  @param limit
**/
export const getAllNotification = async (options) => {
  return await fetchAPI('v3/api-asker-indo/get-notification-not-from-btaskee', options);
};

/**
  @param userId
**/
export const removeAllNotification = async () => {
  return await fetchAPI('v3/api-asker-indo/remove-notification-user');
};

// /**
//   @param userId
//   @param page
//   @param limit
// **/
// export const getAllNotification = async(page, limit) => {
//   const options = {
//     page: page,
//     limit: limit,
//   };
//   return await fetchAPI('v3/api-asker-indo/get-notification', options);
//};

export const getAllReward = async (options) => {
  // TODO V#
  return await fetchAPI('v2/reward/get-all', options);
};

export const removeNotificationById = async (options) => {
  return await fetchAPI('v3/api-asker-indo/remove-notification-by-id', options);
};

export const removeNotificationNotFromBtaskeeById = async (options) => {
  return await fetchAPI('v3/api-asker-indo/remove-notification-not-from-btaskee-by-id', options);
};

export const sendTokenToServer = async (options) => {
  return await fetchAPI('v3/api-asker-indo/init-raix-push-token', options);
};

export const updateIsReadNotification = async (options) => {
  return await fetchAPI('v3/api-asker-indo/update-status-notification', options);
};
