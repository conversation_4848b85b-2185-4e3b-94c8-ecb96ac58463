import { fetchAPI } from '@helper';

/**
  @param userId
  @param page
  @param limit
**/
export const getAllNotification = async (options) => {
  return await fetchAPI('v3/api-asker-th/get-notification-not-from-btaskee', options);
};

/**
  @param userId
**/
export const removeAllNotification = async () => {
  return await fetchAPI('v3/api-asker-th/remove-notification-user');
};

export const getAllReward = async (options) => {
  return await fetchAPI('v2/reward/get-all', options);
};

export const removeNotificationById = async (options) => {
  return await fetchAPI('v3/api-asker-th/remove-notification-by-id', options);
};

export const removeNotificationNotFromBtaskeeById = async (options) => {
  return await fetchAPI('v3/api-asker-th/remove-notification-not-from-btaskee-by-id', options);
};

export const sendTokenToServer = async (options) => {
  return await fetchAPI('v3/api-asker-th/init-raix-push-token', options);
};

export const updateIsReadNotification = async (options) => {
  return await fetchAPI('v3/api-asker-th/update-status-notification', options);
};
