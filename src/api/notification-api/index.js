import * as ID_API from './id';
import * as TH_API from './th';
import * as VN_API from './vn';

/**
  @param userId
  @param page
  @param limit
**/
export const getAllNotification = async (page, limit) => {
  const options = {
    page: page,
    limit: limit,
  };
  const combine = {
    VN: VN_API.getAllNotification,
    TH: TH_API.getAllNotification,
    ID: ID_API.getAllNotification,
  };
  return await combine[global.isoCode](options);
};

/**
  @param userId
**/
export const removeAllNotification = async () => {
  const combine = {
    VN: VN_API.removeAllNotification,
    TH: TH_API.removeAllNotification,
    ID: ID_API.removeAllNotification,
  };
  return await combine[global.isoCode]();
};

export const getAllReward = async () => {
  const options = {
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getAllReward,
    TH: TH_API.getAllReward,
    ID: ID_API.getAllReward,
  };
  return await combine[global.isoCode](options);
};

export const removeNotificationById = async (id) => {
  const options = {
    id: id,
  };
  const combine = {
    VN: VN_API.removeNotificationById,
    TH: TH_API.removeNotificationById,
    ID: ID_API.removeNotificationById,
  };
  return await combine[global.isoCode](options);
};

export const removeNotificationNotFromBtaskeeById = async (id) => {
  const options = {
    id: id,
  };
  const combine = {
    VN: VN_API.removeNotificationNotFromBtaskeeById,
    TH: TH_API.removeNotificationNotFromBtaskeeById,
    ID: ID_API.removeNotificationNotFromBtaskeeById,
  };
  return await combine[global.isoCode](options);
};

export const sendTokenToServer = async (options) => {
  const combine = {
    VN: VN_API.sendTokenToServer,
    TH: TH_API.sendTokenToServer,
    ID: ID_API.sendTokenToServer,
  };
  return await combine[global?.isoCode](options);
};

export const updateIsReadNotification = async (id) => {
  const options = {
    id: id,
  };
  const combine = {
    VN: VN_API.updateIsReadNotification,
    TH: TH_API.updateIsReadNotification,
    ID: ID_API.updateIsReadNotification,
  };
  return await combine[global.isoCode](options);
};
