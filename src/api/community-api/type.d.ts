import { IImagesCommunity, IPostTheme, IVideoCommunity } from '@models/community/news-feed.model';
import { IUserCommunity } from '@models/community/user';
import { DisplayPositionHashTag, TypeReportCommunity } from '@src/lib/config/community';

export interface IParamGetListNewsFeeds {
  tagIds?: string[]; // Nếu chưa đăng nhập
  tagId?: string; // Nếu filter tag cụ thể
  sortKey?: string; // MOST_RECENT, MOST_LIKED, MOST_SHARED
  fromDate?: string; // Nếu filter theo ngày Infinity roll (createAt của record cuối cùng)
  lastNumberOfLikes?: number;
  lastNumberOfShares?: number;
}

export interface IParamSearchPostAPI {
  searchText: string;
  sortKey?: string; // MOST_RECENT, MOST_RELEVANT
  fromDate?: string; // Nếu filter theo ngày Infinity roll (createAt của record cuối cùng)
  lastScore?: number; // dien score cua gia tri cuoi cung sortKey = MOST_RELEVANT
}

export interface IParamGetNewsFeedDetailAPI {
  postId: string;
}

export interface IParamSearchUserAPI {
  searchText?: string;
  fromDate?: Date; // Nếu filter theo ngày Infinity roll (createAt của record cuối cùng)
}

export interface IParamInsertUserCommunityAPI {
  favouriteTagIds?: string[];
}

export interface IParamCreateNewsPost {
  userId: string;
  tagIds: string[];
  content?: string;
  isNeedHelpPost?: boolean;
  images?: IImagesCommunity[];
  videos?: IVideoCommunity[];
  postTheme?: IPostTheme;
}
export interface IParamGetUserProfileCommunityAPI {
  userId?: string;
  profileId?: string;
}

export interface IParamGetUserPostsAPI {
  userId?: string;
  profileId?: string;
}

export interface IParamGetUserSharedPostsAPI {
  userId?: string;
  profileId?: string;
}

export type IParamEditPost = {
  _id: string;
  userId: string;
  tagIds: string[];
  content?: string;
  images?: IImagesCommunity[];
  videos?: IVideoCommunity[];
};

export type IParamDeletePost = {
  userId: string;
  postId: string;
};

export type IParamsGetNotificationsAPI = {
  userId?: string;
  limit: number;
  page: number;
};

export type IParamsSetIsReadNotificationAPI = {
  userId?: string;
  notificationId: string;
};

export type IParamReportUser = {
  reason: {
    name: TypeReportCommunity;
    content?: string;
  };
  profileId?: string;
  postId?: string;
  commentId?: string;
  userId?: string;
};
export type IParamCommentPost = {
  userId: string;
  content: string;
  postId: string;
  mentions?: {
    userId: string;
    username: string;
  }[];
};

export type IParamReplyComment = {
  userId: string;
  content: string;
  commentId: string;
  postId: string;
  mentions?: {
    userId: string;
    username: string;
  }[];
};

export type IParamLikeAndUnlikeComment = {
  userId: string;
  postId: string;
  commentId: string;
};

export type IParamLikeAndUnlikePost = {
  userId: string;
  postId: string;
};

export type IParamGetComments = {
  postId: string;
  fromDate?: string;
  commentId?: string;
};

export type IParamUpdateUserProfile = {
  avatar?: string;
  bio?: string;
  medalIdInUse?: string;
  name?: string;
};

export type IParamSharePost = {
  sharedByPostId?: string;
  content?: string;
};

export interface IParamGetAllHashTags {
  displayPosition: DisplayPositionHashTag;
}

export type IParamFollowUser = {
  userId: string;
  targetUserId: string;
};

export type IParamUnFollowUser = {
  userId: string;
  targetUserId: string;
};

export type IParamGetListFollowing = {
  userId: string;
  targetUserId: string;
  skip?: number;
  searchText?: string;
};

export type IParamGetListFollowers = {
  userId: string;
  targetUserId: string;
  skip?: number;
  searchText?: string;
};

export type IResponseGetListFollowing = {
  listFollowing?: IUserCommunity[];
  numberOfFollowers?: number;
  numberOfFollowing?: number;
};

export type IResponseGetListFollowers = {
  listFollowers?: IUserCommunity[];
  numberOfFollowers?: number;
  numberOfFollowing?: number;
};

export type IParamHideAndUnHidePost = {
  userId: string;
  postId: string;
};

export type IParamBlockAndUnBlockUser = {
  userId: string;
  blockedUserId: string;
};
