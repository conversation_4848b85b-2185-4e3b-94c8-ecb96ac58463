import { fetchAPI } from '@helper';

import { IParamPricingTaskDateOptions } from './type';

/**
 * @param task {{object}}
 * @param service {{object}}
 * @example
 * task :
 *  date: moment(task.date).utc().format(),
    duration: task.duration,
    autoChooseTasker: <PERSON><PERSON><PERSON>(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    taskPlace: task.taskPlace,
    homeType: task.homeType,
    taskId = task._id,
    promotion = { code: task.promotion.code };
    requirements = [{ type: 1}];
    detail = task.detail;
    service :
    {_id: ''}
 */
export const getPriceCleaning = async (options) => {
  return await fetchAPI('v3/pricing-vn/home-cleaning', options, 'post', false);
};

export const getPriceAirConditioner = async (options) => {
  return await fetchAPI('v3/pricing-vn/air-conditioner-v2', options, 'post', false);
};

export const getPriceDeepCleaning = async (options) => {
  return await fetchAPI('v3/pricing-vn/deep-cleaning', options, 'post', false);
};

export const getPriceOfficeCleaning = async (options) => {
  return await fetchAPI('v3/pricing-vn/office-cleaning', options, 'post', false);
};

export const getPriceWashingMachine = async (options) => {
  return await fetchAPI('v3/pricing-vn/washing-machine', options, 'post', false);
};

export const getPriceWaterHeater = async (options) => {
  return await fetchAPI('v3/pricing-vn/water-heater', options, 'post', false);
};

export const getPriceOfficeCarpetCleaning = async (options) => {
  return await fetchAPI('v3/pricing-vn/carpet-cleaning', options, 'post', false);
};

export const getPriceSofaCleaning = async (options) => {
  return await fetchAPI('v3/pricing-vn/sofa', options, 'post', false);
};

export const getPriceHomeMoving = async (options) => {
  return await fetchAPI('v3/pricing-vn/home-moving', options, 'post', false);
};

export const getPriceDisinfection = async (options) => {
  return await fetchAPI('v3/pricing-vn/disinfection', options, 'post', false);
};

export const getPriceHomeCooking = async (options) => {
  return await fetchAPI('v3/pricing-vn/home-cooking', options, 'post', false);
};

export const getPriceGroceryAssistant = async (options) => {
  return await fetchAPI('v3/pricing-vn/grocery-assistant', options, 'post', false);
};

export const getPriceHouseKeeping = async (options) => {
  return await fetchAPI('v3/pricing-vn/housekeeping-v2', options, 'post', false);
};

export const getPriceElderlyCare = async (options) => {
  return await fetchAPI('v3/pricing-vn/elderly-care', options, 'post', false);
};

export const getPricePatientCare = async (options) => {
  return await fetchAPI('v3/pricing-vn/patient-care', options, 'post', false);
};

export const getPriceChildCare = async (options) => {
  return await fetchAPI('v3/pricing-vn/child-care', options, 'post', false);
};

export const pricingTaskDateOptions = async (options: IParamPricingTaskDateOptions) => {
  return await fetchAPI('v3/pricing-vn/task-date-options', options, 'post', false);
};

export const pricingLaundry = async (options) => {
  return await fetchAPI('v3/pricing-vn/laundry', options, 'post', false);
};

export const getPriceIndustrialCleaning = async (options) => {
  return await fetchAPI('v3/pricing-vn/industrial-cleaning', options, 'post', false);
};
