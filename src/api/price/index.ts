import _, { isEmpty } from 'lodash';

import { ID, SERVICES, TH, VN } from '@config';
import { fetchAPI, getIsoCodeGlobal, IRespond } from '@helper';

import * as ID_API from './id';
import * as TH_API from './th';
import { IParamPricingTaskDateOptions } from './type';
import * as VN_API from './vn';

/**
 * @param task {{object}}
 * @param service {{object}}
 * @example
 * task :
 *  date: moment(task.date).utc().format(),
  duration: task.duration,
  autoChooseTasker: Bo<PERSON>an(task.autoChooseTasker),
  taskPlace: task.taskPlace,
  taskPlace: task.taskPlace,
  homeType: task.homeType,
  taskId = task._id,
  promotion = { code: task.promotion.code };
  requirements = [{ type: 1}];
  detail = task.detail;
  service :
  {_id: ''}
 */
export const getPriceCleaning = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceCleaning,
    TH: TH_API.getPriceCleaning,
    ID: ID_API.getPriceCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getPriceAirConditioner = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceAirConditioner,
    TH: TH_API.getPriceAirConditioner,
    ID: ID_API.getPriceAirConditioner,
  };
  return await combine[global.isoCode](options);
};

export const getPriceDeepCleaning = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceDeepCleaning,
    TH: TH_API.getPriceDeepCleaning,
    ID: ID_API.getPriceDeepCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getPriceOfficeCleaning = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getPriceOfficeCleaning,
    TH: TH_API.getPriceOfficeCleaning,
    ID: ID_API.getPriceOfficeCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getPriceOfficeCarpetCleaning = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.getPriceOfficeCarpetCleaning,
    TH: VN_API.getPriceOfficeCarpetCleaning,
    ID: ID_API.getPriceOfficeCarpetCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getPriceHomeCooking = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceHomeCooking,
    TH: null,
    ID: null,
  };
  return await combine[global.isoCode](options);
};

export const getPriceLaundry = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  // new data
  const combine = {
    VN: VN_API.pricingLaundry,
    TH: TH_API.pricingLaundry,
    ID: ID_API.pricingLaundry,
  };
  return await combine[global.isoCode](options);
};

export const getPriceGroceryAssistant = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceGroceryAssistant,
    TH: null,
    ID: null,
  };
  return await combine[global.isoCode](options);
};

export const getPriceSofa = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };

  const combine = {
    VN: VN_API.getPriceSofaCleaning,
    TH: TH_API.getPriceSofaCleaning,
    ID: ID_API.getPriceSofaCleaning,
  };
  return await combine[global.isoCode](options);
};

export const getPriceHouseKeeping = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode || data.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceHouseKeeping,
    TH: TH_API.getPriceHouseKeeping,
    ID: ID_API.getPriceHousekeeping,
  };
  return await combine[global.isoCode](options);
};

export const getPriceElderlyCare = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceElderlyCare,
    TH: TH_API.getPriceElderlyCare,
    ID: ID_API.getPriceElderlyCare,
  };
  return await combine[global.isoCode](options);
};

export const getPricePatientCare = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPricePatientCare,
    TH: TH_API.getPricePatientCare,
    ID: ID_API.getPricePatientCare,
  };
  return await combine[global.isoCode](options);
};

export const getPriceDisinfection = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceDisinfection,
    TH: TH_API.getPriceDisinfection,
    ID: ID_API.getPriceDisinfection,
  };
  return await combine[global.isoCode](options);
};

export const getPriceChildCare = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceChildCare,
    TH: TH_API.getPriceChildCare,
    ID: ID_API.getPriceChildCare,
  };
  return await combine[global.isoCode](options);
};

export const getPriceWashingMachine = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceWashingMachine,
    TH: TH_API.getPriceWashingMachine,
    ID: ID_API.getPriceWashingMachine,
  };
  return await combine[global.isoCode](options);
};

export const getPriceWaterHeater = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceWaterHeater,
    TH: TH_API.getPriceWaterHeater,
    ID: ID_API.getPriceWaterHeater,
  };
  return await combine[global.isoCode](options);
};

export const getPriceHomeMoving = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: VN_API.getPriceHomeMoving,
    TH: TH_API.getPriceHomeMoving,
    ID: ID_API.getPriceHomeMoving,
  };
  return await combine[global.isoCode](options);
};

export const getPriceMassage = async (data) => {
  const options = {
    ...data,
    isoCode: global.isoCode,
    // isMockAPI: true,
  };
  const combine = {
    VN: null,
    TH: TH_API.getPriceMassage,
    ID: ID_API.getPriceMassage,
  };
  return await combine[global.isoCode](options);
};

export const getPriceIndustrialCleaning = async (data) => {
  const isoCode = getIsoCodeGlobal();
  const options = {
    ...data,
    isoCode,
  };
  const combine = {
    VN: VN_API.getPriceIndustrialCleaning,
    TH: TH_API.getPriceIndustrialCleaning,
    ID: ID_API.getPriceIndustrialCleaning,
  };
  return await combine[isoCode](options);
};

/**
 * @param data {{object}} data of task
 * @param serviceName {{string}} name of service
 */
export const getPriceProvider = async (data, serviceName) => {
  if (!isEmpty(data?.task?.dateOptions)) {
    return await pricingTaskDateOptions(data);
  }
  switch (serviceName) {
    case SERVICES.CLEANING:
      return await getPriceCleaning(data);
    case SERVICES.CLEANING_SUBSCRIPTION:
      return await getPriceCleaning(data);
    case SERVICES.AIR_CONDITIONER:
      return await getPriceAirConditioner(data);
    case SERVICES.DEEP_CLEANING:
      return await getPriceDeepCleaning(data);
    case SERVICES.HOME_COOKING:
      return await getPriceHomeCooking(data);
    case SERVICES.LAUNDRY:
      return await getPriceLaundry(data);
    case SERVICES.HOUSE_KEEPING:
      return await getPriceHouseKeeping(data);
    case SERVICES.SOFA:
      return await getPriceSofa(data);
    case SERVICES.GROCERY_ASSISTANT:
      return await getPriceGroceryAssistant(data);
    case SERVICES.ELDERLY_CARE:
      return await getPriceElderlyCare(data);
    case SERVICES.ELDERLY_CARE_SUBSCRIPTION:
      return await getPriceElderlyCare(data);
    case SERVICES.PATIENT_CARE:
      return await getPricePatientCare(data);
    case SERVICES.PATIENT_CARE_SUBSCRIPTION:
      return await getPricePatientCare(data);
    case SERVICES.DISINFECTION_SERVICE:
      return await getPriceDisinfection(data);
    case SERVICES.CHILD_CARE:
      return await getPriceChildCare(data);
    case SERVICES.CHILD_CARE_SUBSCRIPTION:
      return await getPriceChildCare(data);
    case SERVICES.OFFICE_CLEANING:
      return await getPriceOfficeCleaning(data);
    case SERVICES.OFFICE_CLEANING_SUBSCRIPTION:
      return await getPriceOfficeCleaning(data);
    case SERVICES.WASHING_MACHINE:
      return await getPriceWashingMachine(data);
    case SERVICES.WATER_HEATER:
      return await getPriceWaterHeater(data);
    case SERVICES.OFFICE_CARPET_CLEANING:
      return await getPriceOfficeCarpetCleaning(data);
    case SERVICES.HOME_MOVING:
      return await getPriceHomeMoving(data);
    case SERVICES.MASSAGE:
      return await getPriceMassage(data);
    case SERVICES.INDUSTRIAL_CLEANING:
      return await getPriceIndustrialCleaning(data);
    default:
      break;
  }
};

export const pricingTaskDateOptions = (...args: [IParamPricingTaskDateOptions]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.pricingTaskDateOptions,
    [TH]: TH_API.pricingTaskDateOptions,
    [ID]: ID_API.pricingTaskDateOptions,
  };
  const api = apis[getIsoCodeGlobal()];
  return api?.(...args);
};
