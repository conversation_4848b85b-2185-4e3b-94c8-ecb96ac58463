import { fetchAPI } from '@helper';
import { SHA256 } from '@helper/sha256';

/**
 * @param username
 * @param password
 */
export const loginWithPassword = async (username: string, password: string) => {
  return await fetchAPI('v4/user-asker-vn/login', {
    username,
    password: SHA256(password),
    type: 'ASKER',
  });
};

export const loginWithFacebook = async (data: Object) => {
  return await fetchAPI('v4/user-asker-vn/login-facebook', data);
};

export const loginWithGoogle = async (data: Object) => {
  return await fetchAPI('v4/user-asker-vn/login-google', data);
};

export const loginWithApple = async (data: Object) => {
  return await fetchAPI('v4/user-asker-vn/login-apple', data);
};

// /**
//  * @param params
//  * @param loginType
//  * @example
//  * params: { accessToken: '123', email: '<EMAIL>', name: 'pham duc anh', avatar: '' }
//  * loginType: 'facebook'
//  */
// export const loginWithSocial = async(params: object, loginType: string) => {
//   return await fetchAPI('v2/login/social', {params, loginType});
// };

/**
 * @param userId
 * @param token
 */
export const logOut = async (data: Object) => {
  return await fetchAPI('v3/user-asker-vn/logout', data);
};
