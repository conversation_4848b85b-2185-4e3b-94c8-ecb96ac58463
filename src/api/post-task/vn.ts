import { fetchAPI } from '@helper';

import { IParamBookTaskForceTasker } from './type';

/**
 * @param serviceId {{string}}
 * @param date {{date}} date of task
 */
export const checkTaskSameTime = async (options) => {
  return await fetchAPI('v3/api-asker-vn/check-task-sametime', options);
};

export const postTaskCleaning = async (options) => {
  return await fetchAPI('v2/booking/home-cleaning', options, 'post', false);
};

export const postTaskAirConditioner = async (options) => {
  return await fetchAPI('v2/booking/air-conditioner', options, 'post', false);
};

export const postTaskDeepCleaning = async (options) => {
  return await fetchAPI('v2/booking/deep-cleaning', options, 'post', false);
};

export const postTaskSofa = async (options) => {
  return await fetchAPI('v2/booking/sofa', options, 'post', false);
};

export const postTaskDisinfection = async (options) => {
  return await fetchAPI('v2/booking/disinfection', options, 'post', false);
};

export const postTaskOfficeCleaning = async (options) => {
  return await fetchAPI('v2/booking/office-cleaning', options, 'post', false);
};

export const postTaskWashingMachine = async (options) => {
  return await fetchAPI('v2/booking/washing-machine', options, 'post', false);
};

export const postTaskWashingHeater = async (options) => {
  return await fetchAPI('v2/booking/water-heater', options, 'post', false);
};

export const postTaskOfficeCarpetCleaning = async (options) => {
  return await fetchAPI('v2/booking/carpet-cleaning', options, 'post', false);
};

export const postTaskChildCare = async (options) => {
  return await fetchAPI('v2/booking/child-care', options, 'post', false);
};

export const postTaskHomeMoving = async (options) => {
  return await fetchAPI('v2/booking/home-moving', options, 'post', false);
};

export const postIndustrialCleaning = async (options) => {
  return await fetchAPI('v2/booking/industrial-cleaning', options, 'post', false);
};

export const bookTaskForceTasker = async (options: IParamBookTaskForceTasker) => {
  return await fetchAPI('v2/booking/book-task-force-tasker', options, 'post', false);
};

export const postTaskLaundry = async (options) => {
  return await fetchAPI('v2/booking/laundry', options, 'post', false);
};

export const getCategories = async (options) => {
  return await fetchAPI('v3/api-asker-vn/get-category-grocery-assistant', options, 'post', false);
};

export const findPopularProductGrocery = async (options) => {
  return await fetchAPI('v3/api-asker-vn/find-popular-grocery-assistant', options, 'post', false);
};

export const updateShoppingCart = async (options) => {
  return await fetchAPI('v3/api-asker-vn/update-shopping-cart', options, 'post', false);
};

export const getShoppingCart = async () => {
  return await fetchAPI('v3/api-asker-vn/get-shopping-cart-by-userId', {}, 'post', false);
};

export const groceryFindProduct = async (options) => {
  return await fetchAPI('v3/api-asker-vn/find-product-grocery-assistant', options, 'post', false);
};

export const removeProductCart = async (options) => {
  return await fetchAPI('v3/api-asker-vn/remove-shopping-card-by-id', options, 'post', false);
};

export const postTaskHousekeeping = async (options) => {
  return await fetchAPI('v2/booking/housekeeping-v2', options, 'post', false);
};

export const postTaskElderlyCare = async (options) => {
  return await fetchAPI('v2/booking/elderly-care', options, 'post', false);
};

export const postTaskPatientCare = async (options) => {
  return await fetchAPI('v2/booking/patient-care', options, 'post', false);
};
