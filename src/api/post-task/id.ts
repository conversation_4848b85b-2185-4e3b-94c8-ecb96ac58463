import { fetchAPI } from '@helper';

import { IParamBookTaskForceTasker } from './type';

export const checkTaskSameTime = async (options) => {
  return await fetchAPI('v3/api-asker-indo/check-task-sametime', options);
};

export const postTaskCleaning = async (options) => {
  return await fetchAPI('v3/booking-indo/home-cleaning', options, 'post', false);
};

export const postTaskAirConditioner = async (options) => {
  return await fetchAPI('v3/booking-indo/air-conditioner', options, 'post', false);
};

export const postTaskDeepCleaning = async (options) => {
  return await fetchAPI('v3/booking-indo/deep-cleaning', options, 'post', false);
};

export const postTaskOfficeCleaning = async (options) => {
  return await fetchAPI('v3/booking-indo/office-cleaning', options, 'post', false);
};

export const postTaskWashingMachine = async (options) => {
  return await fetchAPI('v3/booking-indo/washing-machine', options, 'post', false);
};

export const postTaskWashingHeater = async (options) => {
  return await fetchAPI('v3/booking-indo/water-heater', options, 'post', false);
};

export const postTaskOfficeCarpetCleaning = async (options) => {
  return await fetchAPI('v3/booking-indo/carpet-cleaning', options, 'post', false);
};

export const postTaskSofa = async (options) => {
  return await fetchAPI('v3/booking-indo/sofa', options, 'post', false);
};

export const postTaskDisinfection = async (options) => {
  return await fetchAPI('v3/booking-indo/disinfection', options, 'post', false);
};

export const postTaskMassage = async (options) => {
  return await fetchAPI('v3/booking-indo/massage', options, 'post', false);
};

export const postTaskHomeMoving = async (options) => {
  return await fetchAPI('v3/booking-indo/home-moving', options, 'post', false);
};

export const postIndustrialCleaning = async (options) => {
  return await fetchAPI('v3/booking-indo/industrial-cleaning', options, 'post', false);
};

export const bookTaskForceTasker = async (options: IParamBookTaskForceTasker) => {
  return await fetchAPI('v3/booking-indo/book-task-force-tasker', options, 'post', false);
};

export const postTaskLaundry = async (options) => {
  return await fetchAPI('v3/booking-indo/laundry', options, 'post', false);
};

export const getCategories = async (options) => {
  return await fetchAPI('v3/api-asker-indo/get-category-grocery-assistant', options, 'post', false);
};

export const findPopularProductGrocery = async (options) => {
  return await fetchAPI('v3/api-asker-indo/find-popular-grocery-assistant', options, 'post', false);
};

export const updateShoppingCart = async (options) => {
  return await fetchAPI('v3/api-asker-indo/update-shopping-cart', options, 'post', false);
};

export const getShoppingCart = async () => {
  return await fetchAPI('v3/api-asker-indo/get-shopping-cart-by-userId', {}, 'post', false);
};

export const groceryFindProduct = async (options) => {
  return await fetchAPI('v3/api-asker-indo/find-product-grocery-assistant', options, 'post', false);
};

export const removeProductCart = async (options) => {
  return await fetchAPI('v3/api-asker-indo/remove-shopping-card-by-id', options, 'post', false);
};

export const postTaskHousekeeping = async (options) => {
  return await fetchAPI('v3/booking-indo/housekeeping-v2', options, 'post', false);
};

export const postTaskElderlyCare = async (options) => {
  return await fetchAPI('v3/booking-indo/elderly-care', options, 'post', false);
};

export const postTaskPatientCare = async (options) => {
  return await fetchAPI('v3/booking-indo/patient-care', options, 'post', false);
};
