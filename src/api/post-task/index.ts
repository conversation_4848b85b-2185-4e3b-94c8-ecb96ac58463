import { isEmpty } from 'lodash';

import { ID, SERVICES, TH, VN } from '@config';
import { fetchAPI, getIsoCodeGlobal, IRespond } from '@helper';

import * as ID_API from './id';
import * as TH_API from './th';
import { IParamBookTaskForceTasker } from './type';
import * as VN_API from './vn';

/**
 * @param serviceId {{string}}
 * @param date {{date}} date of task
 */
export const checkTaskSameTime = async (serviceId, taskDate) => {
  const options = {
    // isMockAPI: true,
    serviceId,
    taskDate,
    isoCode: global.isoCode,
  };
  const combine = {
    VN: VN_API.checkTaskSameTime,
    TH: VN_API.checkTaskSameTime,
    ID: ID_API.checkTaskSameTime,
  };
  return await combine[global.isoCode](options);
};

export const postTaskCleaning = async (dataTask) => {
  const options = {
    // isMockAPI: true,
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskCleaning,
    TH: TH_API.postTaskCleaning,
    ID: ID_API.postTaskCleaning,
  };
  return await combine[global.isoCode](options);
};

export const postTaskAirConditioner = async (dataTask) => {
  const options = {
    // isMockAPI: true,
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskAirConditioner,
    TH: TH_API.postTaskAirConditioner,
    ID: ID_API.postTaskAirConditioner,
  };
  return await combine[global.isoCode](options);
};

export const postTaskLaundry = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskLaundry,
    TH: TH_API.postTaskLaundry,
    ID: ID_API.postTaskLaundry,
  };
  return await combine[global.isoCode](options);
};

export const postTaskDeepCleaning = async (dataTask) => {
  const options = {
    // isMockAPI: true,
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskDeepCleaning,
    TH: TH_API.postTaskDeepCleaning,
    ID: ID_API.postTaskDeepCleaning,
  };
  return await combine[global.isoCode](options);
};

export const postTaskHomeCooking = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  return await fetchAPI('v2/booking/home-cooking', options, 'post', false);
};

export const postTaskSofa = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskSofa,
    TH: TH_API.postTaskSofa,
    ID: ID_API.postTaskSofa,
  };
  return await combine[global.isoCode](options);
};

export const postTaskGroceryAssistant = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  return await fetchAPI('v2/booking/grocery-assistant', options, 'post', false);
};

export const postTaskGroceryAssistantV3 = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  return await fetchAPI('v3/booking/grocery-assistant', options, 'post', false);
};

export const postTaskHousekeeping = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskHousekeeping,
    TH: TH_API.postTaskHousekeeping,
    ID: ID_API.postTaskHousekeeping,
  };
  return await combine[global.isoCode](options);
};

export const postTaskElderlyCare = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    [VN]: VN_API.postTaskElderlyCare,
    [TH]: TH_API.postTaskElderlyCare,
    [ID]: ID_API.postTaskElderlyCare,
  };
  return await combine[getIsoCodeGlobal()](options);
};

export const postTaskPatientCare = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskPatientCare,
    TH: TH_API.postTaskPatientCare,
    ID: ID_API.postTaskPatientCare,
  };
  return await combine[global.isoCode](options);
};

export const postTaskDisinfection = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskDisinfection,
    TH: TH_API.postTaskDisinfection,
    ID: ID_API.postTaskDisinfection,
  };
  return await combine[global.isoCode](options);
};

export const postTaskChildCare = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskChildCare,
    TH: TH_API.postTaskChildCare,
    ID: null,
  };
  return await combine[global.isoCode](options);
};

export const postTaskOfficeCleaning = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskOfficeCleaning,
    TH: TH_API.postTaskOfficeCleaning,
    ID: ID_API.postTaskOfficeCleaning,
  };
  return await combine[global.isoCode](options);
};

export const postTaskOfficeCarpetCleaning = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskOfficeCarpetCleaning,
    TH: TH_API.postTaskOfficeCarpetCleaning,
    ID: ID_API.postTaskOfficeCarpetCleaning,
  };
  return await combine[global.isoCode](options);
};

export const postTaskWashingMachine = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskWashingMachine,
    TH: null,
    ID: ID_API.postTaskWashingMachine,
  };
  return await combine[global.isoCode](options);
};

export const postTaskWaterHeater = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskWashingHeater,
    TH: TH_API.postTaskWashingHeater,
    ID: ID_API.postTaskWashingHeater,
  };
  return await combine[global.isoCode](options);
};

export const postTaskHomeMoving = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postTaskHomeMoving,
    TH: TH_API.postTaskHomeMoving,
    ID: ID_API.postTaskHomeMoving,
  };
  return await combine[global.isoCode](options);
};

export const postTaskMassage = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: null,
    TH: TH_API.postTaskMassage,
    ID: ID_API.postTaskMassage,
  };
  return await combine[global.isoCode](options);
};

export const postIndustrialCleaning = async (dataTask) => {
  const options = {
    ...dataTask,
  };
  const combine = {
    VN: VN_API.postIndustrialCleaning,
    TH: TH_API.postIndustrialCleaning,
    ID: ID_API.postIndustrialCleaning,
  };
  return await combine[global.isoCode](options);
};

export const postTaskProvider = async (dataTask, serviceName) => {
  if (dataTask?.dateOptions?.length <= 1) {
    delete dataTask.dateOptions;
  }
  if (!isEmpty(dataTask?.forceTasker)) {
    return await bookTaskForceTasker(dataTask);
  }
  switch (serviceName) {
    case SERVICES.CLEANING:
      return await postTaskCleaning(dataTask);
    case SERVICES.AIR_CONDITIONER:
      return await postTaskAirConditioner(dataTask);
    case SERVICES.LAUNDRY:
      return await postTaskLaundry(dataTask);
    case SERVICES.DEEP_CLEANING:
      return await postTaskDeepCleaning(dataTask);
    case SERVICES.HOME_COOKING:
      return await postTaskHomeCooking(dataTask);
    case SERVICES.SOFA:
      return await postTaskSofa(dataTask);
    case SERVICES.GROCERY_ASSISTANT:
      return await postTaskGroceryAssistantV3(dataTask);
    case SERVICES.HOUSE_KEEPING:
      return await postTaskHousekeeping(dataTask);
    case SERVICES.ELDERLY_CARE:
      return await postTaskElderlyCare(dataTask);
    case SERVICES.PATIENT_CARE:
      return await postTaskPatientCare(dataTask);
    case SERVICES.DISINFECTION_SERVICE:
      return await postTaskDisinfection(dataTask);
    case SERVICES.CHILD_CARE:
      return await postTaskChildCare(dataTask);
    case SERVICES.OFFICE_CLEANING:
      return await postTaskOfficeCleaning(dataTask);
    case SERVICES.WASHING_MACHINE:
      return await postTaskWashingMachine(dataTask);
    case SERVICES.WATER_HEATER:
      return await postTaskWaterHeater(dataTask);
    case SERVICES.OFFICE_CARPET_CLEANING:
      return await postTaskOfficeCarpetCleaning(dataTask);
    case SERVICES.HOME_MOVING:
      return await postTaskHomeMoving(dataTask);
    case SERVICES.MASSAGE:
      return await postTaskMassage(dataTask);
    case SERVICES.INDUSTRIAL_CLEANING:
      return await postIndustrialCleaning(dataTask);
    default:
      break;
  }
};

export const getCategories = async (categoryId, storeId) => {
  const options = {
    categoryId: categoryId,
    storeId: storeId,
  };
  const combine = {
    VN: VN_API.getCategories,
    TH: TH_API.getCategories,
    ID: ID_API.getCategories,
  };
  return await combine[global.isoCode](options);
};

export const findPopularProductGrocery = async (storeId) => {
  const options = { storeId: storeId };
  const combine = {
    VN: VN_API.findPopularProductGrocery,
    TH: TH_API.findPopularProductGrocery,
    ID: ID_API.findPopularProductGrocery,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description product search
 * @param {text, language, page} Find by text
 * @param {subCategoryId, page} find subCategory
 * @param {productId} find by product
 */

export const groceryFindProduct = async ({ text, language, subCategoryId, productId, page = 1, limit, storeId }) => {
  const options = { storeId: storeId };

  // Find by text
  if (text && language) {
    options.text = text;
    options.language = language;
    options.page = page;
  }

  // Find subCategory
  if (subCategoryId) {
    options.subCategoryId = subCategoryId;
    options.page = page;
  }

  if (productId) {
    options.productId = productId;
  }

  if (limit) {
    options.limit = limit;
  }
  const combine = {
    VN: VN_API.groceryFindProduct,
    TH: TH_API.groceryFindProduct,
    ID: ID_API.groceryFindProduct,
  };
  return await combine[global.isoCode](options);
};

/**
 * @description Update shoping cart
 *
 * @param userId
 * @param action String ('INCREASE', 'DECREASE', 'REMOVE')
 *
 * @param newProduct: {price, text, categoryId, quantity}  For user add manual
 * @param isUserCreated : true (user add manual)
 *
 * **/
export const updateShoppingCart = async ({ productId, action, price, text, isUserCreated, categoryId, quantity }) => {
  const options = {
    action: action,
  };

  if (productId) {
    options.productId = productId;
  }

  if (quantity) {
    options.quantity = quantity;
  }

  // This param for user add product manual
  if (isUserCreated) {
    options.newProduct = {
      text: text,
      price: price,
      categoryId: categoryId,
    };
    options.isUserCreated = true;
  }
  const combine = {
    VN: VN_API.updateShoppingCart,
    TH: TH_API.updateShoppingCart,
    ID: ID_API.updateShoppingCart,
  };
  return await combine[global.isoCode](options);
};

export const getShoppingCart = async () => {
  const combine = {
    VN: VN_API.getShoppingCart,
    TH: TH_API.getShoppingCart,
    ID: ID_API.getShoppingCart,
  };
  return await combine[global.isoCode]();
};

export const removeProductCart = async (shoppingCardId) => {
  const options = {
    shoppingCardId: shoppingCardId,
  };
  const combine = {
    VN: VN_API.removeProductCart,
    TH: TH_API.removeProductCart,
    ID: ID_API.removeProductCart,
  };
  return await combine[global.isoCode](options);
};

export const bookTaskForceTasker = (...args: [IParamBookTaskForceTasker]): Promise<IRespond<any>> => {
  const apis = {
    [VN]: VN_API.bookTaskForceTasker,
    [TH]: TH_API.bookTaskForceTasker,
    [ID]: ID_API.bookTaskForceTasker,
  };
  const api = apis[getIsoCodeGlobal()];
  return api?.(...args);
};
