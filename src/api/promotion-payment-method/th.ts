import DeviceInfo from 'react-native-device-info';

import { fetchAPI, getIsoCodeGlobal, getUserIdGlobal } from '@helper';

export const getPromotionPaymentMethodAPI = async () => {
  const params = {
    userId: getUserIdGlobal(),
    isoCode: getIsoCodeGlobal(),
    appVersion: DeviceInfo.getVersion().toString(),
  };

  return await fetchAPI('v3/api-asker-th/get-promotion-payment-method', params);
};
