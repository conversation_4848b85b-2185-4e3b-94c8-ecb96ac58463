import * as ID_API from './id';
import * as TH_API from './th';
import { IAddFavoriteServiceParams } from './type';
import * as VN_API from './vn';

export const addFavoriteServiceAPI = async (params: IAddFavoriteServiceParams) => {
  const options: IAddFavoriteServiceParams = {
    isoCode: global.isoCode,
    userId: global.userId,
    ...params,
  };
  const combine = {
    VN: VN_API.addFavoriteService,
    TH: TH_API.addFavoriteService,
    ID: ID_API.addFavoriteService,
  };
  return await combine[global.isoCode](options);
};
