import * as ID_API from './id';
import * as TH_API from './th';
import * as VN_API from './vn';

export const getOutstandingPaymentDebt = async (options) => {
  const combine = {
    VN: VN_API.getOutstandingPaymentDebt,
    TH: TH_API.getOutstandingPaymentDebt,
    ID: ID_API.getOutstandingPaymentDebt,
  };
  return await combine[global.isoCode](options);
};

export const repayOutstanding = async (options) => {
  const combine = {
    VN: VN_API.repayOutstanding,
    TH: TH_API.repayOutstanding,
    ID: ID_API.repayOutstanding,
  };
  return await combine[global.isoCode](options);
};
