---
type: "manual"
---

# Super App Workspace - Augment Agent Rules

## 🎯 PRIORITY: Always Use @btaskee/design-system First

**MANDATORY RULE**: Before using any React Native components, libraries, or utilities, ALWAYS check if an equivalent exists in `@btaskee/design-system` and use it instead.

## 🔍 Information Gathering - ALWAYS Research First

**BEFORE making any code changes:**

1. **Use codebase-retrieval** to understand existing patterns and implementations
2. **Search for similar components/features** in the codebase before creating new ones
3. **Check design system availability** for components, hooks, and utilities
4. **Understand the full context** of the change before implementing

```tsx
// ✅ ALWAYS RESEARCH FIRST
// 1. Search for existing implementations
// 2. Check @btaskee/design-system for components
// 3. Understand the codebase patterns
// 4. Then implement following established patterns
```

## 🎨 Design System First - Mandatory Usage

### Core Components (MUST USE)
```tsx
// ✅ ALWAYS USE DESIGN SYSTEM COMPONENTS
import { 
  BlockView,         // Instead of View
  CText,            // Instead of Text
  ScrollView,       // Instead of ScrollView  
  FlatList,         // Instead of FlatList
  TouchableOpacity, // Instead of TouchableOpacity
  CTextInput,       // Instead of TextInput
  PrimaryButton,    // Instead of Button
  FastImage,        // Instead of Image
  CustomModal,      // Instead of Modal
} from '@btaskee/design-system';
```

### Design Tokens (MANDATORY)
```tsx
// ✅ ALWAYS USE DESIGN SYSTEM TOKENS
import { 
  ColorsV2,      // PREFERRED color system (not Colors)
  Spacing,       // For margins, padding, gaps
  FontSizes,     // For font sizes
  FontFamily,    // For font families
  BorderRadius,  // For border radius
} from '@btaskee/design-system';

// ✅ CORRECT COLOR USAGE
ColorsV2.orange500        // PRIMARY color
ColorsV2.green500         // SECONDARY color
ColorsV2.neutral800       // BLACK text
ColorsV2.neutralWhite     // WHITE backgrounds
ColorsV2.neutralBackground // BACKGROUND color
ColorsV2.neutral100       // BORDER color
ColorsV2.neutral400       // GREY text

// ✅ CORRECT SPACING USAGE
Spacing.SPACE_04   // 4px
Spacing.SPACE_08   // 8px
Spacing.SPACE_16   // 16px
Spacing.SPACE_24   // 24px
```

## 🏗️ Architecture Patterns - Follow Established Conventions

### State Management
```tsx
// ✅ USE ZUSTAND STORES (preferred)
import { useAppStore } from '@/stores/appStore';

// ✅ USE DESIGN SYSTEM HOOKS
import { useI18n, useAppLoading } from '@btaskee/design-system';

// ❌ AVOID Redux when Zustand is available
```

### File Structure & Naming
```tsx
// ✅ FOLLOW ESTABLISHED PATTERNS
components/
  ComponentName/
    index.tsx          // Main component
    ComponentName.tsx  // Implementation
    styles.ts         // Styles (if needed)
    types.ts          // TypeScript types

// ✅ USE CONSISTENT NAMING
- PascalCase for components
- camelCase for functions/variables
- UPPER_CASE for constants
- kebab-case for file names (when appropriate)
```

## 🧪 Testing - Comprehensive E2E with Detox

### Mandatory Testing Patterns
```tsx
// ✅ ALWAYS USE testID for element selection
<PrimaryButton testID="booking-confirm-button">
  Confirm Booking
</PrimaryButton>

// ✅ NEVER use text selectors in tests
// ❌ WRONG: await element(by.text('Confirm')).tap();
// ✅ CORRECT: await element(by.id('booking-confirm-button')).tap();

// ✅ USE scroll-to-reveal patterns
await waitFor(element(by.id('target-element')))
  .toBeVisible()
  .whileElement(by.id('scroll-container'))
  .scroll(150, 'down'); // Incremental scrolling

// ✅ VALIDATE sequential booking flows
// Address → Service → DateTime → Payment
```

### Performance Targets
- **E2E Test Duration**: 3-5 minutes maximum
- **Incremental Scrolling**: ~150 pixels (not aggressive swipes)
- **State Management**: Use Zustand stores for test state

## 📱 Mobile Development Best Practices

### Package Management
```bash
# ✅ ALWAYS USE PNPM
pnpm install package-name
pnpm add -D dev-package

# ❌ DON'T manually edit package.json
```

### Internationalization
```tsx
// ✅ USE DESIGN SYSTEM I18N
import { useI18n } from '@btaskee/design-system';

const { t } = useI18n('namespace');
const text = t('key');

// ✅ USE getTextWithLocale for static text
import { getTextWithLocale } from '@btaskee/design-system';
```

### Navigation & Routing
```tsx
// ✅ USE NavigationHelper from design system
import { NavigationHelper } from '@btaskee/design-system';

NavigationHelper.navigate('ScreenName', params);
```

## 🔄 Migration Patterns - Modernization Guidelines

### Component Migration
```tsx
// ✅ WHEN MIGRATING COMPONENTS:
// 1. Preserve exact UI design and functionality
// 2. Replace with ColorsV2 colors
// 3. Use Spacing utilities
// 4. Use FontSizes from design system
// 5. Replace Redux with Zustand stores
// 6. Add proper null checks
// 7. Use design system components

// ✅ MIGRATION EXAMPLE
// OLD:
<View style={{ backgroundColor: '#ffffff', padding: 16 }}>
  <Text style={{ fontSize: 16, color: '#000000' }}>Title</Text>
</View>

// NEW:
<BlockView backgroundColor={ColorsV2.neutralWhite} padding={Spacing.SPACE_16}>
  <CText size={FontSizes.SIZE_16} color={ColorsV2.neutral800}>Title</CText>
</BlockView>
```

### Service Creation
```tsx
// ✅ COPY FROM EXISTING SERVICES
// When creating new microservices:
// 1. Copy from existing service structure
// 2. Adapt to new requirements
// 3. Follow established patterns
// 4. Use design system components
```

## 🚫 What NOT to Do

### Avoid Direct React Native Usage
```tsx
// ❌ DON'T USE THESE DIRECTLY
import { 
  View,              // Use BlockView
  Text,              // Use CText
  TextInput,         // Use CTextInput
  Button,            // Use PrimaryButton
  Image,             // Use FastImage
  Modal,             // Use CustomModal
} from 'react-native';

// ❌ DON'T USE HARDCODED VALUES
const styles = {
  container: {
    backgroundColor: '#ffffff',  // Use ColorsV2.neutralWhite
    padding: 16,                 // Use Spacing.SPACE_16
    fontSize: 14,                // Use FontSizes.SIZE_14
  }
};

// ❌ DON'T USE OLD CONSTANTS
import { PRIMARY_COLOR } from './constants';  // Use ColorsV2.orange500
```

### Avoid Anti-Patterns
```tsx
// ❌ DON'T combine locale and country changes
// ✅ KEEP them as separate functionalities

// ❌ DON'T use text selectors in tests
// ✅ ALWAYS use testID-based selection

// ❌ DON'T use aggressive scrolling in tests
// ✅ USE incremental scrolling (~150px)
```

## 🎯 Development Workflow

### Before Starting Any Task
1. **Research** existing implementations with codebase-retrieval
2. **Plan** the approach using established patterns
3. **Check** design system for available components
4. **Follow** migration patterns for modernization
5. **Test** with comprehensive E2E tests

### Code Quality Standards
- **Type Safety**: Use TypeScript strictly
- **Null Checks**: Always add proper null/undefined checks
- **Performance**: Target 3-5 minute E2E test execution
- **Consistency**: Follow established naming and structure patterns
- **Accessibility**: Use design system's built-in accessibility features

## 🎯 This Rule Applies To

- ALL React Native development in this monorepo
- ALL apps in `apps/` directory
- ALL remotes in `remotes/` directory  
- ALL packages that use React Native components
- ALL testing with Detox framework
- ALL component migrations and modernizations

**Remember: Research First, Design System First, Test Thoroughly! 🎨🔍🧪**

## 🏗️ Module Federation & Monorepo Architecture

### Understanding the Architecture
```tsx
// ✅ UNDERSTAND THE STRUCTURE
super-app-workspace/
├── apps/
│   └── host/                    # Main host application
├── remotes/                     # Micro-frontends
│   ├── auth/                   # Authentication service
│   ├── payment/                # Payment service
│   ├── service-cleaning/       # Cleaning service
│   ├── service-massage/        # Massage service
│   ├── service-home-moving/    # Home moving service
│   └── ...                     # Other services
└── packages/
    └── design-system/          # Shared design system
```

### Module Federation Patterns
```tsx
// ✅ EXPOSE COMPONENTS CORRECTLY
// In rspack.config.mjs
exposes: {
  './MainNavigator': './src/navigation/MainNavigator.tsx',
  './SpecificComponent': './src/screens/Component/index.tsx',
}

// ✅ IMPORT FEDERATED MODULES
const RemoteComponent = React.lazy(() => import('remoteName/ComponentName'));

// ✅ USE ERROR BOUNDARIES FOR REMOTE MODULES
<ErrorBoundary name="RemoteService">
  <Suspense fallback={<LoadingComponent />}>
    <RemoteComponent />
  </Suspense>
</ErrorBoundary>
```

### Path Aliases & TypeScript Configuration
```tsx
// ✅ CONSISTENT PATH ALIASES ACROSS REMOTES
"paths": {
  "@components": ["src/components"],
  "@screens": ["src/screens"],
  "@store": ["src/store"],
  "@hooks": ["src/hooks"],
  "@navigation/*": ["src/navigation/*"],
  "@config": ["src/config"],
  "@constant": ["src/constant"],
  "@i18n": ["src/i18n"],
  "@types": ["src/types"],
  "@images": ["src/assets/images"],
  "@e2e/*": ["e2e/*"]
}
```

## 🗃️ State Management - Zustand Patterns

### Store Structure & Best Practices
```tsx
// ✅ ZUSTAND STORE PATTERN
import { createZustand, AppStorage } from '@btaskee/design-system';
import { createJSONStorage, persist } from 'zustand/middleware';

interface AppState {
  // State properties
  data: SomeType;
  isLoading: boolean;

  // Actions
  setData: (data: SomeType) => void;
  resetState: () => void;
}

export const useAppStore = createZustand<AppState>()(
  persist(
    (set) => ({
      // Initial state
      data: null,
      isLoading: false,

      // Actions
      setData: (data) => set({ data }),
      resetState: () => set({ data: null, isLoading: false }),
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
    },
  ),
);
```

### Store Usage Patterns
```tsx
// ✅ USE STORES IN COMPONENTS
const Component = () => {
  const { data, isLoading, setData } = useAppStore();

  // Use store state and actions
  return <BlockView>{/* component content */}</BlockView>;
};

// ✅ SELECTIVE STORE SUBSCRIPTIONS
const Component = () => {
  const isLoading = useAppStore((state) => state.isLoading);
  // Only re-renders when isLoading changes
};
```

## 🌐 API & Error Handling Patterns

### API Request Patterns
```tsx
// ✅ USE DESIGN SYSTEM API HELPERS
import { apiRequest, EndpointKeys } from '@btaskee/design-system';

// ✅ PROPER ERROR HANDLING
const fetchData = async () => {
  try {
    const response = await apiRequest({
      key: EndpointKeys.getData,
      params: { id: '123' },
    });
    return response;
  } catch (error) {
    // Handle specific error types
    if (error.code === 401) {
      // Handle auth error
      NavigationHelper.navigate('Login');
    } else if (error.code === 422) {
      // Handle validation error
      AlertHelper.show({
        title: t('VALIDATION_ERROR'),
        message: error.message,
      });
    } else {
      // Generic error handling
      RequestHelpers.handleError(error);
    }
    throw error;
  }
};
```

### Error Boundary Implementation
```tsx
// ✅ USE ERROR BOUNDARIES FOR REMOTE MODULES
<ErrorBoundary name="ServiceName">
  <RemoteServiceComponent />
</ErrorBoundary>

// ✅ CUSTOM ERROR COMPONENTS
const ErrorComponent = ({ error, retry }: { error: Error; retry: () => void }) => (
  <BlockView center padding={Spacing.SPACE_16}>
    <CText color={ColorsV2.neutral800}>{t('ERROR_OCCURRED')}</CText>
    <PrimaryButton onPress={retry} title={t('RETRY')} />
  </BlockView>
);
```

## 🌍 Internationalization (i18n) Patterns

### i18n Usage Patterns
```tsx
// ✅ USE DESIGN SYSTEM I18N HOOK
import { useI18n } from '@btaskee/design-system';

const Component = () => {
  const { t } = useI18n('namespace');

  return (
    <BlockView>
      <CText>{t('WELCOME_MESSAGE')}</CText>
      <CText>{t('USER_COUNT', { count: userCount })}</CText>
    </BlockView>
  );
};

// ✅ USE getTextWithLocale FOR STATIC TEXT
import { getTextWithLocale } from '@btaskee/design-system';

const staticText = getTextWithLocale(
  { vi: 'Xin chào', en: 'Hello', th: 'สวัสดี' },
  currentLocale
);
```

### Locale Management
```tsx
// ✅ USE APP STORE FOR LOCALE
const { locale, onChangeLocale } = useAppStore();

// ✅ HANDLE LOCALE CHANGES
const handleLocaleChange = (newLocale: LOCALES) => {
  onChangeLocale(newLocale);
  // App will reload to ensure proper state refresh
};
```

## 🧭 Navigation Patterns

### Navigation Structure
```tsx
// ✅ USE DESIGN SYSTEM NAVIGATION HELPER
import { NavigationHelper } from '@btaskee/design-system';

// ✅ NAVIGATION ACTIONS
NavigationHelper.navigate('ScreenName', { param: 'value' });
NavigationHelper.goBack();
NavigationHelper.reset('ScreenName');

// ✅ CONDITIONAL NAVIGATION BASED ON E2E TESTING
const initialRouteName = useMemo(() => {
  if (isFirstOpen && !ConfigHelpers.isE2ETesting) {
    return RouteName.IntroNavigator;
  }
  return RouteName.MainNavigator;
}, [isFirstOpen]);
```

### Screen Configuration
```tsx
// ✅ SCREEN OPTIONS WITH I18N
const screenOptions = {
  title: t('SCREEN_TITLE'),
  headerTitleStyle: {
    fontFamily: FontFamily.bold,
    fontSize: FontSizes.SIZE_16
  },
  animationEnabled: !ConfigHelpers.isE2ETesting, // Disable for E2E
};
```

## 📝 TypeScript Patterns & Conventions

### Type Definitions
```tsx
// ✅ CONSISTENT TYPE PATTERNS
export interface IComponentProps {
  data: Maybe<SomeType>;
  onPress?: () => void;
  isLoading?: boolean;
}

// ✅ USE DESIGN SYSTEM TYPES
import { Maybe, IObjectText, IRespond } from '@btaskee/design-system';

// ✅ API RESPONSE TYPES
interface ApiResponse<T> extends IRespond<T> {
  data?: Maybe<T>;
  error?: SerializedError;
}

// ✅ MULTILINGUAL TEXT TYPE
interface IMultilingualText extends IObjectText {
  vi: string;
  en: string;
  th?: string;
  id?: string;
}
```

### Enum & Constants
```tsx
// ✅ USE ENUMS FOR CONSTANTS
export enum BOOKING_STATUS {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

// ✅ API ERROR CODES
export enum API_ERROR_CODE {
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500,
}
```

## 🎣 Custom Hooks Patterns

### Data Management Hooks
```tsx
// ✅ CUSTOM HOOK PATTERN
export const useChangeData = () => {
  const { setData, getData } = useAppStore();
  const { getPrice } = usePostTask();

  const onChangeData = useCallback((newData: DataType) => {
    setData(newData);
    getPrice(); // Trigger dependent actions
  }, [setData, getPrice]);

  return { onChangeData };
};

// ✅ API HOOKS WITH ERROR HANDLING
export const useApiData = (id: string) => {
  const [data, setData] = useState<Maybe<DataType>>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Maybe<Error>>(null);

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await apiRequest({
        key: EndpointKeys.getData,
        params: { id },
      });
      setData(response);
    } catch (err) {
      setError(err as Error);
      RequestHelpers.handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  useEffect(() => {
    if (id) fetchData();
  }, [id, fetchData]);

  return { data, isLoading, error, refetch: fetchData };
};
```

## 🧪 Enhanced Testing Patterns

### E2E Test Structure
```tsx
// ✅ E2E TEST FILE STRUCTURE
describe('FILE: e2e/service-name/booking-flow.spec.js', () => {
  beforeEach(async () => {
    await initData('resetData');
    await device.reloadReactNative();
  });

  it('LINE X - Complete booking flow: Address → Service → DateTime → Payment', async () => {
    // Address selection
    await tapId('address-input');
    await typeText('address-input', 'Test Address');
    await tapId('address-confirm');

    // Service selection with scroll-to-reveal
    await scrollToElement('service-selection-container', 'service-massage');
    await tapId('service-massage');

    // DateTime selection
    await tapId('datetime-picker');
    await selectDateTime();

    // Payment confirmation
    await scrollToElement('payment-container', 'payment-confirm');
    await tapId('payment-confirm');

    // Verify success
    await waitForElement('booking-success', 5000);
    await expect(element(by.id('booking-success'))).toBeVisible();
  });
});
```

### Test Utilities
```tsx
// ✅ REUSABLE TEST FUNCTIONS
const scrollToElement = async (containerId: string, targetId: string) => {
  await waitFor(element(by.id(targetId)))
    .toBeVisible()
    .whileElement(by.id(containerId))
    .scroll(150, 'down'); // Incremental scrolling
};

const selectService = async (serviceId: string) => {
  await scrollToElement('service-list', serviceId);
  await tapId(serviceId);
  await waitForElement('service-selected', 3000);
};
```

## 🔧 Development Workflow Enhancements

### Pre-Development Checklist
1. **Research Phase**
   - Use `codebase-retrieval` to understand existing patterns
   - Check design system for available components
   - Review similar implementations in other remotes
   - Understand the module federation setup

2. **Planning Phase**
   - Create task breakdown with meaningful units (~20 min each)
   - Plan state management approach (Zustand stores)
   - Design API integration patterns
   - Plan testing strategy (E2E flows)

3. **Implementation Phase**
   - Follow established file structure patterns
   - Use consistent TypeScript patterns
   - Implement proper error handling
   - Add comprehensive testIDs for E2E testing

4. **Testing Phase**
   - Write E2E tests with sequential flow validation
   - Use incremental scrolling patterns
   - Target 3-5 minute execution times
   - Validate all user journeys

### Code Quality Standards
```tsx
// ✅ COMPONENT STRUCTURE
const ComponentName: React.FC<IComponentProps> = ({
  data,
  onPress,
  isLoading = false
}) => {
  // Hooks
  const { t } = useI18n('namespace');
  const { someData } = useAppStore();

  // Handlers
  const handlePress = useCallback(() => {
    if (onPress) onPress();
  }, [onPress]);

  // Early returns
  if (!data) return <LoadingComponent />;

  // Main render
  return (
    <BlockView testID="component-container">
      <CText testID="component-title">{t('TITLE')}</CText>
      <PrimaryButton
        testID="component-action"
        onPress={handlePress}
        title={t('ACTION')}
        loading={isLoading}
      />
    </BlockView>
  );
};
```

**Remember: Research First, Design System First, Test Thoroughly! 🎨🔍🧪**
