import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useState,
} from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ScrollView, StatusBar } from 'react-native';
import { DragSortableView } from 'react-native-drag-sort';
import {
  Al<PERSON>,
  AnimationHelpers,
  BlockView,
  ColorsV2,
  ConditionView,
  ConfigHelpers,
  CText,
  DeviceHelper,
  EndpointKeys,
  getTextWithLocale,
  handleError,
  HitSlop,
  IService,
  NavBar,
  PrimaryButton,
  RouteName,
  SizedBox,
  Spacing,
  ToastHelpers,
  TouchableOpacity,
  useApiMutation,
  useAppLoading,
  useAppStore,
  useI18n,
  useSettingsStore,
  useUserStore,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';
import { isEmpty } from 'lodash-es';

import { ServiceItem } from '@components';
import { useAppNavigation } from '@hooks';
import { ParamsNavigationList } from '@navigation/type';

import { styles } from './styles';

type ExploreBTaskeeScreenProps = StackScreenProps<
  ParamsNavigationList,
  RouteName.ExploreBTaskee
>;

const MAX_FAVORITE_ITEM = 8;
const MIN_FAVORITE_ITEM = 4;
const WIDTH_CONTENT_CONTAINER =
  DeviceHelper.WINDOW.WIDTH - Spacing.SPACE_12 * 4 - 2;
const WIDTH_ITEM = WIDTH_CONTENT_CONTAINER / 4;

//Dịch vụ ưa thích,
export const ExploreBTaskeeScreen = ({ route }: ExploreBTaskeeScreenProps) => {
  const promotionCode = route.params?.promotionCode;
  const navigation = useAppNavigation();
  const { t } = useI18n('common');
  const { t: tHost } = useI18n('host');
  const { showAppLoading, hideAppLoading } = useAppLoading();
  const { isoCode } = useAppStore();
  const { isHadOpenExploreBtaskee, setIsHadOpenExploreBtaskee } = useAppStore();
  const isFocused = useIsFocused();

  const { mutate: addFavoriteService } = useApiMutation({
    key: EndpointKeys.addFavoriteService,
    options: {
      onMutate: () => {
        showAppLoading();
      },
      onSettled: () => {
        hideAppLoading();
      },
    },
  });

  const { user } = useUserStore();
  const { updateFavoriteService } = useSettingsStore();
  const serviceGroup = useSettingsStore()?.settings?.serviceGroup;
  const favouriteServices =
    useSettingsStore()?.settings?.favouriteServices || [];

  const [favoriteList, setFavoriteList] =
    useState<IService[]>(favouriteServices);
  // TODO
  // Đánh dấu mở trực tiếp, không hiện action chỉnh sửa lần đầu tiên
  const isOpenDefault = route.params?.isOpenDefault; //TODO: remove
  const isEditDefault = isOpenDefault
    ? false
    : !isEmpty(user) && !isHadOpenExploreBtaskee && !ConfigHelpers.isE2ETesting; //Nếu lần đầu tiên vào và login thì edit sẽ mở cho edit đầu tiên

  const [isEdit, setIsEdit] = useState(isEditDefault);
  const [scrollEnabled, setScrollEnabled] = useState(true);

  useEffect(() => {
    if (isEditDefault) {
      setIsHadOpenExploreBtaskee(true);
    }
  }, [isEditDefault, setIsHadOpenExploreBtaskee]);

  useEffect(() => {
    navigation.setOptions({
      headerShown: true,
      title: tHost('EXPLORE.TITLE'),
    });
  }, [navigation, tHost]);

  const serviceGroupList: IService[] = useMemo(() => {
    if (favoriteList?.length) {
      const newList = serviceGroup?.map((item) => {
        const newServices = item?.services?.filter((el) => {
          const isFavorite = favoriteList?.some(
            (favorite) => favorite?._id === el?._id,
          );
          return !isFavorite;
        });
        return {
          ...item,
          services: newServices,
        };
      });
      return newList;
    }
    return serviceGroup;
  }, [favoriteList, serviceGroup]);

  const favoriteListRender = useMemo(() => {
    const length = favoriteList.length;
    const arr = Array(MAX_FAVORITE_ITEM).fill(null).slice(length);
    if (isEdit && length < MAX_FAVORITE_ITEM) {
      return [...favoriteList, ...arr];
    }
    return favoriteList;
  }, [favoriteList, isEdit]);

  //Kiểm tra người dùng có thay đổi list favorite hay không
  const isChangeFavoriteList = useMemo(() => {
    let isChange = false;
    if (!isEmpty(user) && favoriteList.length) {
      isChange =
        JSON.stringify(favoriteList) !== JSON.stringify(favouriteServices);
    }
    return isChange;
  }, [favoriteList, favouriteServices, user]);

  const alertConfirmCancel = useCallback(
    (onExit: () => void) => {
      Alert.alert.open({
        title: tHost('EXPLORE.TITLE_MODAL_CANCEL'),
        message: tHost('EXPLORE.MESSAGE_MODAL_CANCEL'),
        actions: [
          {
            text: t('EXIT'),
            style: 'cancel',
            onPress: onExit,
          },
          {
            text: tHost('EXPLORE.CONTINUE_EDIT'),
          },
        ],
      });
    },
    [t, tHost],
  );

  const onGoBack = useCallback(() => {
    if (isChangeFavoriteList) {
      alertConfirmCancel(navigation.goBack);
    } else {
      navigation.canGoBack() && navigation.goBack();
    }
  }, [alertConfirmCancel, isChangeFavoriteList, navigation]);

  const backAction = useCallback(() => {
    if (isChangeFavoriteList) {
      alertConfirmCancel(navigation.goBack);
      return true;
    }
    return false;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isChangeFavoriteList]);

  const optionToolBar = useMemo(() => {
    return (
      <NavBar
        title={tHost('EXPLORE.TITLE')}
        onBack={onGoBack}
        isShowBackButton={true}
      />
    );
  }, [onGoBack, tHost]);

  useLayoutEffect(() => {
    navigation.setOptions({
      header: () => optionToolBar,
    });
  }, [navigation, optionToolBar]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    if (!isFocused) {
      backHandler.remove();
    }
    return () => backHandler.remove();
  }, [backAction, isFocused]);

  const toggleEdit = () => {
    setIsEdit(!isEdit);
  };

  const setInitialFavoriteList = () => {
    toggleEdit();
    setFavoriteList(favouriteServices);
  };

  const onCancelEdit = () => {
    if (isChangeFavoriteList) {
      alertConfirmCancel(setInitialFavoriteList);
    } else {
      setInitialFavoriteList();
    }
  };

  const onAdd = (item: IService) => () => {
    AnimationHelpers.runLayoutAnimation();
    if (favoriteList.length >= MAX_FAVORITE_ITEM) {
      ToastHelpers.showWarning({
        message: tHost('EXPLORE.MAX_FAVORITE_MESSAGE', {
          max: MAX_FAVORITE_ITEM,
        }),
      });
    } else {
      const newList = [...favoriteList, item];
      setFavoriteList(newList);
    }
  };

  const onMinus = (item: IService) => () => {
    AnimationHelpers.runLayoutAnimation();
    if (favoriteList.length <= MIN_FAVORITE_ITEM) {
      ToastHelpers.showWarning({
        message: tHost('EXPLORE.MIN_FAVORITE_MESSAGE', {
          min: MIN_FAVORITE_ITEM,
        }),
      });
    } else {
      const newList = favoriteList.filter((el) => item?._id !== el._id);
      setFavoriteList(newList);
    }
  };

  const onSave = async () => {
    try {
      showAppLoading();
      const favouriteServiceIds = favoriteList.map((el) => el._id as string);
      addFavoriteService(
        { favouriteServiceIds, isoCode },
        {
          onSuccess: () => {
            toggleEdit();
            updateFavoriteService(favoriteList);
          },
          onError: (error) => {
            handleError(error);
          },
        },
      );
    } catch (error) {
    } finally {
      hideAppLoading();
    }
  };

  const listHeaderComponent = () => {
    if (isEmpty(favoriteList)) {
      return null;
    }
    return (
      <BlockView style={styles.blockContainer}>
        <BlockView style={styles.headerBlock}>
          <CText
            size={16}
            bold
            flex
          >
            {tHost('EXPLORE.FAVORITE_SERVICES')}
          </CText>
          <ConditionView
            condition={isEdit}
            viewTrue={
              <BlockView row>
                <PrimaryButton
                  testID="cancelFavouriteServiceBtn"
                  title={t('CANCEL')}
                  titleColor={ColorsV2.green500}
                  color={ColorsV2.neutral100}
                  titleProps={{ style: { fontSize: 12 } }}
                  style={styles.button}
                  onPress={onCancelEdit}
                />
                <ConditionView
                  condition={isChangeFavoriteList}
                  viewTrue={
                    <>
                      <SizedBox width={Spacing.SPACE_12} />
                      <PrimaryButton
                        testID="saveFavouriteServiceBtn"
                        title={t('SAVE')}
                        titleProps={{ style: { fontSize: 12 } }}
                        style={styles.button}
                        onPress={onSave}
                      />
                    </>
                  }
                />
              </BlockView>
            }
            viewFalse={
              <TouchableOpacity
                testID="editFavouriteServiceBtn"
                hitSlop={HitSlop.SMALL}
                activeOpacity={0.7}
                onPress={toggleEdit}
              >
                <CText color={ColorsV2.green500}>{tHost('EXPLORE.EDIT')}</CText>
              </TouchableOpacity>
            }
          />
        </BlockView>
        <ConditionView
          condition={isEdit}
          viewTrue={
            <BlockView style={styles.desFavoriteContainer}>
              <CText
                color={ColorsV2.green500}
                size={12}
              >
                {tHost('EXPLORE.DES_FAVORITE_SERVICE')}
              </CText>
            </BlockView>
          }
        />
        <BlockView style={styles.itemServiceContainer}>
          <DragSortableView
            childrenWidth={WIDTH_ITEM}
            childrenHeight={WIDTH_ITEM * 0.6 + 80}
            parentWidth={WIDTH_CONTENT_CONTAINER}
            dataSource={favoriteListRender}
            keyExtractor={(item, index) => item?._id || index.toString()}
            renderItem={(item) => (
              <ServiceItem
                // entryPoint={TrackingScreenNames.ServiceList}
                testID={`postTaskService${item?.name}`}
                service={item}
                isShowMinus={isEdit}
                widthItem={WIDTH_ITEM}
                onPressMinus={onMinus(item)}
                promotionCode={promotionCode}
              />
            )}
            onDragStart={() => {
              // Bắt đầu drag thì không cho scroll
              setScrollEnabled(false);
            }}
            onClickItem={(_, item, i) => {
              onMinus(item)();
            }}
            onDragEnd={() => {
              // End drag thì bật lại scroll
              setScrollEnabled(true);
            }}
            onDataChange={(data) => {
              const newList = data.filter((e) => e);
              setFavoriteList(newList);
            }}
          />
        </BlockView>
      </BlockView>
    );
  };

  const renderService = (services: IService[]) => {
    return (
      <BlockView style={styles.itemServiceContainer}>
        {services.map((service) => {
          return (
            <ServiceItem
              // entryPoint={TrackingScreenNames.ServiceList}
              testID={`postTaskService${service?.name}`}
              key={service._id}
              service={service}
              isShowPlus={isEdit}
              widthItem={WIDTH_ITEM}
              promotionCode={promotionCode}
              onPressPlus={onAdd(service)}
            />
          );
        })}
      </BlockView>
    );
  };

  const content = () => {
    if (isEdit) {
      //check nêu service được add hết thì sẽ ẩn luôn block
      const isEmptyServiceGroup = isEmpty(
        serviceGroupList?.filter((item) => !isEmpty(item?.services)),
      );
      if (isEmptyServiceGroup) return null;

      return (
        <BlockView style={styles.blockContainer}>
          <BlockView style={styles.headerBlock}>
            <CText
              size={16}
              bold
            >
              {tHost('EXPLORE.OTHER_SERVICES')}
            </CText>
          </BlockView>
          <BlockView>
            {serviceGroupList?.map((item) => {
              if (!item?.services?.length) return null;
              return (
                <BlockView
                  key={item?._id}
                  margin={{ top: Spacing.SPACE_16 }}
                >
                  <CText bold>{getTextWithLocale(item.text)}</CText>
                  {renderService(item.services)}
                </BlockView>
              );
            })}
          </BlockView>
        </BlockView>
      );
    }
    return (
      <>
        {serviceGroupList?.map((item) => {
          if (!item?.services?.length) return null;
          return (
            <BlockView
              key={item?._id}
              style={styles.blockContainer}
            >
              <BlockView style={styles.headerBlock}>
                <CText
                  size={16}
                  bold
                >
                  {getTextWithLocale(item.text)}
                </CText>
              </BlockView>
              {renderService(item.services)}
            </BlockView>
          );
        })}
      </>
    );
  };

  if (isEmpty(serviceGroup)) {
    return (
      <BlockView style={styles.container}>
        <StatusBar backgroundColor="transparent" />
        <BlockView
          flex
          center
        >
          <CText bold>{t('COMING_SOON')}</CText>
        </BlockView>
      </BlockView>
    );
  }

  return (
    <BlockView style={styles.container}>
      <StatusBar backgroundColor="transparent" />
      <ScrollView
        testID="scrollExploreBTaskeeScreen"
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        scrollEnabled={scrollEnabled}
      >
        {listHeaderComponent()}
        {content()}
      </ScrollView>
    </BlockView>
  );
};
