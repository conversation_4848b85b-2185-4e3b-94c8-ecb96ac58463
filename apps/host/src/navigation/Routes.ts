import React from 'react';
import { ConfigHelpers, RouteName } from '@btaskee/design-system';

import { AuthScreen } from '@screens/Auth';
import { ExploreBTaskeeScreen } from '@screens/ExploreBTaskee';
import { IntroExploreBTaskeeScreen } from '@screens/IntroExploreBtaskee';
import { PaymentScreen } from '@screens/Payment';
import { AirConditionerScreen } from '@screens/Services/AirConditioner';
import { ChildCareScreen } from '@screens/Services/ChildCare';
import { ChildCareSubscriptionScreen } from '@screens/Services/ChildCareSubscription';
import { CleaningScreen } from '@screens/Services/Cleaning';
import { CleaningSubscriptionScreen } from '@screens/Services/CleaningSubscription';
import { DeepCleaningScreen } from '@screens/Services/DeepCleaning';
import { DisinfectionScreen } from '@screens/Services/Disinfection';
import { ElderlyCareScreen } from '@screens/Services/ElderlyCare';
import { ElderlyCareSubscriptionScreen } from '@screens/Services/ElderlyCareSubscription';
import { HomeCookingScreen } from '@screens/Services/HomeCooking';
import { HomeMovingScreen } from '@screens/Services/HomeMoving';
import { IndustrialCleaningScreen } from '@screens/Services/IndustrialCleaning';
import { IroningScreen } from '@screens/Services/Ironing';
import { LaundryScreen } from '@screens/Services/Laundry';
import { MassageScreen } from '@screens/Services/Massage';
import { OfficeCarpetCleaningScreen } from '@screens/Services/OfficeCarpetCleaning';
import { OfficeCleaningScreen } from '@screens/Services/OfficeCleaning';
import { OfficeCleaningSubscriptionScreen } from '@screens/Services/OfficeCleaningSubscription';
import { PatientCareScreen } from '@screens/Services/PatientCare';
import { PatientCareSubscriptionScreen } from '@screens/Services/PatientCareSubscription';
import { SofaCleaningScreen } from '@screens/Services/SofaCleaning';
import { SofaCleaningThailandScreen } from '@screens/Services/SofaCleaningThailand';
import { WashingMachineScreen } from '@screens/Services/WashingMachine';
import { WaterHeaterScreen } from '@screens/Services/WaterHeater';
import { TaskManagementScreen } from '@screens/TaskManagements';
import { VoiceChatScreen } from '@screens/VoiceChat';

import { TabsNavigator } from './TabNavigator';
import { MainStackParamList } from './type';

export const ROUTES: {
  name: keyof MainStackParamList;
  component: React.ComponentType<any>;
}[] = [
  {
    name: RouteName.TabNavigator,
    component: TabsNavigator,
  },
  {
    name: RouteName.DeepCleaningService,
    component: DeepCleaningScreen,
  },
  {
    name: RouteName.CleaningService,
    component: CleaningScreen,
  },
  {
    name: RouteName.AirConditionerService,
    component: AirConditionerScreen,
  },
  {
    name: RouteName.VoiceChat,
    component: VoiceChatScreen,
  },
  {
    name: RouteName.Auth,
    component: AuthScreen,
  },
  {
    name: RouteName.CleaningSubscriptionService,
    component: CleaningSubscriptionScreen,
  },
  {
    name: RouteName.ChildCareService,
    component: ChildCareScreen,
  },
  {
    name: RouteName.ChildCareSubscriptionService,
    component: ChildCareSubscriptionScreen,
  },
  {
    name: RouteName.ElderlyCareService,
    component: ElderlyCareScreen,
  },
  {
    name: RouteName.PatientCareService,
    component: PatientCareScreen,
  },
  {
    name: RouteName.OfficeCleaningService,
    component: OfficeCleaningScreen,
  },
  {
    name: RouteName.OfficeCleaningSubscriptionService,
    component: OfficeCleaningSubscriptionScreen,
  },
  {
    name: RouteName.ElderlyCareSubscriptionService,
    component: ElderlyCareSubscriptionScreen,
  },
  {
    name: RouteName.PatientCareSubscriptionService,
    component: PatientCareSubscriptionScreen,
  },
  {
    name: RouteName.WaterHeaterService,
    component: WaterHeaterScreen,
  },
  {
    name: RouteName.WashingMachineService,
    component: WashingMachineScreen,
  },
  {
    name: RouteName.DisinfectionService,
    component: DisinfectionScreen,
  },
  {
    name: RouteName.HomeCookingService,
    component: HomeCookingScreen,
  },
  {
    name: RouteName.OfficeCarpetCleaningService,
    component: OfficeCarpetCleaningScreen,
  },
  {
    name: RouteName.TaskManagement,
    component: TaskManagementScreen,
  },
  {
    name: RouteName.ExploreBTaskee,
    component: ExploreBTaskeeScreen,
  },
  {
    name: RouteName.IntroExploreBTaskee,
    component: IntroExploreBTaskeeScreen,
  },
  {
    name: RouteName.SofaCleaningService,
    component: SofaCleaningScreen,
  },
  {
    name: RouteName.IndustrialCleaningService,
    component: IndustrialCleaningScreen,
  },
  {
    name: RouteName.SofaCleaningThailandService,
    component: SofaCleaningThailandScreen,
  },
  {
    name: RouteName.MassageService,
    component: MassageScreen,
  },
  {
    name: RouteName.IroningService,
    component: IroningScreen,
  },
  {
    name: RouteName.LaundryService,
    component: LaundryScreen,
  },
  {
    name: RouteName.HomeMovingService,
    component: HomeMovingScreen,
  },
  {
    name: RouteName.Payment,
    component: PaymentScreen,
  },
];

export const getRoutes = () => {
  if (ConfigHelpers.isE2ETesting && ConfigHelpers.initialRouteName) {
    return ROUTES.filter(
      (route) => route.name === ConfigHelpers.initialRouteName,
    );
  }
  return ROUTES;
};
